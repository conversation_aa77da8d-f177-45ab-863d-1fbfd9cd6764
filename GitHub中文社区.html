<!DOCTYPE html><html lang="zh-CN"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/microsoft?size=48"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/google?size=48"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/facebook?size=48"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/apache?size=48"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/alibaba?size=48"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/Tencent?size=48"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/didi?size=48"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/bilibili?size=48"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/bytedance?size=48"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/adobe?size=48"/><link rel="stylesheet" href="/_next/static/css/a8a3af5f429da1cc.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/275e79cfb732fbdf.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/864df8640d6e275a.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/3caf7682e0d0c7f6.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/7369cfca97536b38.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/15a7f50c65b3acc7.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/fd637fbbdf907f32.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/9d85d1493ae1b434.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/1aa66934733428bb.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/a4515bee888d92d1.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/19067771cc4d3ee8.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/13fe034d1d788ff0.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-c5bd16fb9e28489f.js"/><script src="/_next/static/chunks/4bd1b696-1b89db9e97752d2d.js" async=""></script><script src="/_next/static/chunks/1517-cb478a1a8ecd69a2.js" async=""></script><script src="/_next/static/chunks/main-app-2e5788c1099c469f.js" async=""></script><script src="/_next/static/chunks/3deddded-68201c8d81665792.js" async=""></script><script src="/_next/static/chunks/baf71385-d0c999ba092b5fe2.js" async=""></script><script src="/_next/static/chunks/8173-d098b821394bd879.js" async=""></script><script src="/_next/static/chunks/5805-1abbbe5bb126872b.js" async=""></script><script src="/_next/static/chunks/482-9be9609530651154.js" async=""></script><script src="/_next/static/chunks/5445-da8093459f30f6a0.js" async=""></script><script src="/_next/static/chunks/9435-088317c1b3cdacf0.js" async=""></script><script src="/_next/static/chunks/7461-439a77a375650687.js" async=""></script><script src="/_next/static/chunks/9699-ad33edfb261796d9.js" async=""></script><script src="/_next/static/chunks/866-3757d5cd14c89021.js" async=""></script><script src="/_next/static/chunks/1641-f6301c7124da702e.js" async=""></script><script src="/_next/static/chunks/app/layout-861711e7ab4f45f5.js" async=""></script><script src="/_next/static/chunks/5565-c0772e77e216bce8.js" async=""></script><script src="/_next/static/chunks/4584-6111dc905d81e591.js" async=""></script><script src="/_next/static/chunks/3562-eee04cad71fcedad.js" async=""></script><script src="/_next/static/chunks/5377-84470fbb5a85c869.js" async=""></script><script src="/_next/static/chunks/5632-8a88983323da71bf.js" async=""></script><script src="/_next/static/chunks/3033-8eea8d07aec5e7d5.js" async=""></script><script src="/_next/static/chunks/6054-607a988849a9c9b2.js" async=""></script><script src="/_next/static/chunks/3446-8e433dfa2fac8253.js" async=""></script><script src="/_next/static/chunks/app/page-18e0f5b7f45a1756.js" async=""></script><script src="/_next/static/chunks/app/loading-0e11eedaa8ea8b58.js" async=""></script><link rel="preload" href="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js" as="script"/><link rel="preload" href="https://www.googletagmanager.com/gtag/js?id=G-WSRH260442" as="script"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/baidu?size=48"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/Meituan-Dianping?size=48"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/JoeanAmier?size=35"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/OI-wiki?size=35"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/XayahSuSuSu?size=35"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/MatsuriDayo?size=35"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/fanmingming?size=35"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/coolsnowwolf?size=35"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/Genesis-Embodied-AI?size=35"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/Shubhamsaboo?size=35"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/frappe?size=35"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/juspay?size=35"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/daveebbelaar?size=35"/><link rel="preload" as="image" href="https://static.github-zh.com/github_avatars/tldr-pages?size=35"/><title>GitHub中文社区</title><meta name="description" content="重要提示原githubs.cn已无法访问，域名已变更为github-zh.com。GitHub是世界上最大的代码托管平台，超7千万开发者正在使用。GitHub中文社区，是国内领先的开源社区，是一个帮您发现GitHub上优质开源项目的地方。提供GitHub趋势，GitHub排行榜，GitHub分类检索，中文翻译等实用功能"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script><style data-styled="" data-styled-version="5.3.11">.fHrHav{display:grid;grid-template-columns:auto auto auto auto 1fr;grid-template-areas:'context-area context-area context-area context-area context-area' 'leading-action  breadcrumbs title-area trailing-action actions' 'description description description description description' 'navigation navigation navigation navigation navigation';}/*!sc*/
.fHrHav:has([data-component="TitleArea"][data-size-variant="large"]){font-size:var(--custom-font-size,var(--text-title-size-large,2rem));line-height:var(--custom-line-height,var(--text-title-lineHeight-large,1.5));font-weight:var(--custom-font-weight,var(--base-text-weight-normal,400));--title-line-height:var(--custom-line-height,var(--text-title-lineHeight-large,1.5));}/*!sc*/
.fHrHav:has([data-component="TitleArea"][data-size-variant="medium"]){font-size:var(--custom-font-size,var(--text-title-size-medium,1.25rem));line-height:var(--custom-line-height,var(--text-title-lineHeight-medium,1.6));font-weight:var(--custom-font-weight,var(--base-text-weight-semibold,600));--title-line-height:var(--custom-line-height,var(--text-title-lineHeight-medium,1.6));}/*!sc*/
.fHrHav:has([data-component="TitleArea"][data-size-variant="subtitle"]){font-size:var(--custom-font-size,var(--text-title-size-medium,1.25rem));line-height:var(--custom-line-height,var(--text-title-lineHeight-medium,1.6));font-weight:var(--custom-font-weight,var(--base-text-weight-normal,400));--title-line-height:var(--custom-line-height,var(--text-title-lineHeight-medium,1.6));}/*!sc*/
.fHrHav [data-component="PH_LeadingAction"],.fHrHav [data-component="PH_TrailingAction"],.fHrHav [data-component="PH_Actions"],.fHrHav [data-component="PH_LeadingVisual"],.fHrHav [data-component="PH_TrailingVisual"]{height:calc(var(--title-line-height) * 1em);}/*!sc*/
.cpOVOS{grid-row:2;grid-area:title-area;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;gap:0.5rem;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;-webkit-align-items:flex-start;-webkit-box-align:flex-start;-ms-flex-align:flex-start;align-items:flex-start;}/*!sc*/
.enysYT{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-order:0;-ms-flex-order:0;order:0;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;}/*!sc*/
.hFAVwB{display:block;-webkit-order:1;-ms-flex-order:1;order:1;font-size:inherit;font-weight:inherit;}/*!sc*/
.gUkoLg{-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;}/*!sc*/
.bnmsDs{grid-row:2;grid-area:actions;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;padding-left:0.5rem;gap:0.5rem;min-width:-webkit-max-content;min-width:-moz-max-content;min-width:max-content;-webkit-box-pack:right;-webkit-justify-content:right;-ms-flex-pack:right;justify-content:right;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;}/*!sc*/
.eItYAW{margin-top:0;margin-bottom:0;padding-left:0;}/*!sc*/
data-styled.g4[id="Box-sc-g0xbh4-0"]{content:"fHrHav,cpOVOS,enysYT,hFAVwB,gUkoLg,bnmsDs,eItYAW,"}/*!sc*/
.ctABYc{border-radius:6px;border:1px solid;border-color:var(--button-default-borderColor-rest,var(--button-default-borderColor-rest,var(--color-btn-border,rgba(31,35,40,0.15))));font-family:inherit;font-weight:500;font-size:14px;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-text-decoration:none;text-decoration:none;text-align:center;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between;height:32px;padding:0 12px;gap:8px;min-width:-webkit-max-content;min-width:-moz-max-content;min-width:max-content;-webkit-transition:80ms cubic-bezier(0.65,0,0.35,1);transition:80ms cubic-bezier(0.65,0,0.35,1);-webkit-transition-property:color,fill,background-color,border-color;transition-property:color,fill,background-color,border-color;color:var(--button-default-fgColor-rest,var(--color-btn-text,#24292f));background-color:var(--button-default-bgColor-rest,var(--color-btn-bg,#f6f8fa));box-shadow:var(--button-default-shadow-resting,var(--color-btn-shadow,0 1px 0 rgba(31,35,40,0.04))),var(--button-default-shadow-inset,var(--color-btn-inset-shadow,inset 0 1px 0 rgba(255,255,255,0.25)));}/*!sc*/
.ctABYc:focus:not(:disabled){box-shadow:none;outline:2px solid var(--fgColor-accent,var(--color-accent-fg,#0969da));outline-offset:-2px;}/*!sc*/
.ctABYc:focus:not(:disabled):not(:focus-visible){outline:solid 1px transparent;}/*!sc*/
.ctABYc:focus-visible:not(:disabled){box-shadow:none;outline:2px solid var(--fgColor-accent,var(--color-accent-fg,#0969da));outline-offset:-2px;}/*!sc*/
.ctABYc[href]{display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;}/*!sc*/
.ctABYc[href]:hover{-webkit-text-decoration:none;text-decoration:none;}/*!sc*/
.ctABYc:hover{-webkit-transition-duration:80ms;transition-duration:80ms;}/*!sc*/
.ctABYc:active{-webkit-transition:none;transition:none;}/*!sc*/
.ctABYc[data-inactive]{cursor:auto;}/*!sc*/
.ctABYc:disabled{cursor:not-allowed;box-shadow:none;color:var(--fgColor-disabled,var(--color-primer-fg-disabled,#8c959f));border-color:var(--button-default-borderColor-disabled,var(--button-default-borderColor-rest,var(--color-btn-border,rgba(31,35,40,0.15))));background-color:var(--button-default-bgColor-disabled,var(--control-bgColor-disabled,var(--color-input-disabled-bg,rgba(175,184,193,0.2))));}/*!sc*/
.ctABYc:disabled [data-component=ButtonCounter]{color:inherit;}/*!sc*/
@media (forced-colors:active){.ctABYc:focus{outline:solid 1px transparent;}}/*!sc*/
.ctABYc [data-component=ButtonCounter]{font-size:12px;background-color:var(--buttonCounter-default-bgColor-rest,var(--color-btn-counter-bg,rgba(31,35,40,0.08)));}/*!sc*/
.ctABYc[data-component=IconButton]{display:inline-grid;padding:unset;place-content:center;width:32px;min-width:unset;}/*!sc*/
.ctABYc[data-size="small"]{padding:0 8px;height:28px;gap:4px;font-size:12px;}/*!sc*/
.ctABYc[data-size="small"] [data-component="text"]{line-height:1.6666667;}/*!sc*/
.ctABYc[data-size="small"] [data-component=ButtonCounter]{font-size:12px;}/*!sc*/
.ctABYc[data-size="small"] [data-component="buttonContent"] > :not(:last-child){margin-right:4px;}/*!sc*/
.ctABYc[data-size="small"][data-component=IconButton]{width:28px;padding:unset;}/*!sc*/
.ctABYc[data-size="large"]{padding:0 16px;height:40px;gap:8px;}/*!sc*/
.ctABYc[data-size="large"] [data-component="buttonContent"] > :not(:last-child){margin-right:8px;}/*!sc*/
.ctABYc[data-size="large"][data-component=IconButton]{width:40px;padding:unset;}/*!sc*/
.ctABYc[data-block="block"]{width:100%;}/*!sc*/
.ctABYc[data-label-wrap="true"]{min-width:-webkit-fit-content;min-width:-moz-fit-content;min-width:fit-content;height:unset;min-height:var(--control-medium-size,2rem);}/*!sc*/
.ctABYc[data-label-wrap="true"] [data-component="buttonContent"]{-webkit-flex:1 1 auto;-ms-flex:1 1 auto;flex:1 1 auto;-webkit-align-self:stretch;-ms-flex-item-align:stretch;align-self:stretch;padding-block:calc(var(--control-medium-paddingBlock,0.375rem) - 2px);}/*!sc*/
.ctABYc[data-label-wrap="true"] [data-component="text"]{white-space:unset;word-break:break-word;}/*!sc*/
.ctABYc[data-label-wrap="true"][data-size="small"]{height:unset;min-height:var(--control-small-size,1.75rem);}/*!sc*/
.ctABYc[data-label-wrap="true"][data-size="small"] [data-component="buttonContent"]{padding-block:calc(var(--control-small-paddingBlock,0.25rem) - 2px);}/*!sc*/
.ctABYc[data-label-wrap="true"][data-size="large"]{height:unset;min-height:var(--control-large-size,2.5rem);padding-inline:var(--control-large-paddingInline-spacious,1rem);}/*!sc*/
.ctABYc[data-label-wrap="true"][data-size="large"] [data-component="buttonContent"]{padding-block:calc(var(--control-large-paddingBlock,0.625rem) - 2px);}/*!sc*/
.ctABYc[data-inactive]:not([disabled]){background-color:var(--button-inactive-bgColor,var(--button-inactive-bgColor-rest,var(--color-btn-inactive-bg,#eaeef2)));border-color:var(--button-inactive-bgColor,var(--button-inactive-bgColor-rest,var(--color-btn-inactive-bg,#eaeef2)));color:var(--button-inactive-fgColor,var(--button-inactive-fgColor-rest,var(--color-btn-inactive-text,#57606a)));}/*!sc*/
.ctABYc[data-inactive]:not([disabled]):focus-visible{box-shadow:none;}/*!sc*/
.ctABYc [data-component="leadingVisual"]{grid-area:leadingVisual;}/*!sc*/
.ctABYc [data-component="text"]{grid-area:text;line-height:1.4285714;white-space:nowrap;}/*!sc*/
.ctABYc [data-component="trailingVisual"]{grid-area:trailingVisual;}/*!sc*/
.ctABYc [data-component="trailingAction"]{margin-right:-4px;}/*!sc*/
.ctABYc [data-component="buttonContent"]{-webkit-flex:1 0 auto;-ms-flex:1 0 auto;flex:1 0 auto;display:grid;grid-template-areas:"leadingVisual text trailingVisual";grid-template-columns:min-content minmax(0,auto) min-content;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-align-content:center;-ms-flex-line-pack:center;align-content:center;}/*!sc*/
.ctABYc [data-component="buttonContent"] > :not(:last-child){margin-right:8px;}/*!sc*/
.ctABYc [data-component="loadingSpinner"]{grid-area:text;margin-right:0px !important;place-self:center;color:var(--fgColor-muted,var(--color-fg-muted,#656d76));}/*!sc*/
.ctABYc [data-component="loadingSpinner"] + [data-component="text"]{visibility:hidden;}/*!sc*/
.ctABYc:hover:not([disabled]):not([data-inactive]){background-color:var(--button-default-bgColor-hover,var(--color-btn-hover-bg,#f3f4f6));border-color:var(--button-default-borderColor-hover,var(--button-default-borderColor-hover,var(--color-btn-hover-border,rgba(31,35,40,0.15))));}/*!sc*/
.ctABYc:active:not([disabled]):not([data-inactive]){background-color:var(--button-default-bgColor-active,var(--color-btn-active-bg,hsla(220,14%,93%,1)));border-color:var(--button-default-borderColor-active,var(--button-default-borderColor-active,var(--color-btn-active-border,rgba(31,35,40,0.15))));}/*!sc*/
.ctABYc[aria-expanded=true]{background-color:var(--button-default-bgColor-active,var(--color-btn-active-bg,hsla(220,14%,93%,1)));border-color:var(--button-default-borderColor-active,var(--button-default-borderColor-active,var(--color-btn-active-border,rgba(31,35,40,0.15))));}/*!sc*/
.ctABYc [data-component="leadingVisual"],.ctABYc [data-component="trailingVisual"],.ctABYc [data-component="trailingAction"]{color:var(--button-color,var(--fgColor-muted,var(--color-fg-muted,#656d76)));}/*!sc*/
.ctABYc[data-component="IconButton"][data-no-visuals]:not(:disabled){color:var(--fgColor-muted,var(--color-fg-muted,#656d76));}/*!sc*/
.ctABYc[data-size="small"][data-no-visuals]{display:inline-block;}/*!sc*/
data-styled.g5[id="types__StyledButton-sc-ws60qy-0"]{content:"ctABYc,"}/*!sc*/
.kRIKrw{display:inline-block;overflow:hidden;line-height:1;vertical-align:middle;border-radius:clamp(4px,var(--avatar-size) - 24px,6px);box-shadow:0 0 0 1px var(--avatar-borderColor,var(--color-avatar-border,rgba(31,35,40,0.15)));height:var(--avatar-size);width:var(--avatar-size);--avatar-size:35px;}/*!sc*/
data-styled.g15[id="Avatar__StyledAvatar-sc-2lv0r8-0"]{content:"kRIKrw,"}/*!sc*/
.bpSOTI{display:inline-block;white-space:nowrap;list-style:none;}/*!sc*/
.bpSOTI::after{font-size:14px;content:'';display:inline-block;height:0.8em;margin:0 0.5em;border-right:0.1em solid;border-color:var(--fgColor-muted,var(--color-fg-muted,#656d76));-webkit-transform:rotate(15deg) translateY(0.0625em);-ms-transform:rotate(15deg) translateY(0.0625em);transform:rotate(15deg) translateY(0.0625em);}/*!sc*/
.bpSOTI:first-child{margin-left:0;}/*!sc*/
.bpSOTI:last-child::after{content:none;}/*!sc*/
data-styled.g28[id="Breadcrumbs__Wrapper-sc-9m4wsf-0"]{content:"bpSOTI,"}/*!sc*/
.cJTTqz{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between;}/*!sc*/
data-styled.g29[id="Breadcrumbs__BreadcrumbsBase-sc-9m4wsf-1"]{content:"cJTTqz,"}/*!sc*/
.hztLzL{color:var(--fgColor-accent,var(--color-accent-fg,#0969da));display:inline-block;font-size:14px;-webkit-text-decoration:none;text-decoration:none;}/*!sc*/
.hztLzL:hover,.hztLzL:focus{-webkit-text-decoration:underline;text-decoration:underline;}/*!sc*/
.hztLzL.selected{color:var(--fgColor-default,var(--color-fg-default,#1F2328));pointer-events:none;}/*!sc*/
.hztLzL.selected:focus{-webkit-text-decoration:none;text-decoration:none;}/*!sc*/
data-styled.g30[id="Breadcrumbs__BreadcrumbsItem-sc-9m4wsf-2"]{content:"hztLzL,"}/*!sc*/
</style></head><body><div color="fg.default" font-family="normal" data-portal-root="true" data-color-mode="light" data-light-theme="light" data-dark-theme="dark" class="BaseStyles__Base-sc-nfjs56-0 dKtIvR"><div><div class="Header d-flex flex-justify-between flex-md-justify-start" style="z-index:1;position:relative"><div class="mr-3"><!--$!--><template data-dgst="BAILOUT_TO_CLIENT_SIDE_RENDERING"></template><!--/$--></div><div class="Header-item mr-3"><a class="Header-link f4 d-flex flex-items-center" href="/"><svg aria-hidden="true" focusable="false" class="octicon octicon-mark-github" viewBox="0 0 24 24" width="32" height="32" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M12.5.75C6.146.75 1 5.896 1 12.25c0 5.089 3.292 9.387 7.863 10.91.575.101.79-.244.79-.546 0-.273-.014-1.178-.014-2.142-2.889.532-3.636-.704-3.866-1.35-.13-.331-.69-1.352-1.18-1.625-.402-.216-.977-.748-.014-.762.906-.014 1.553.834 1.769 1.179 1.035 1.74 2.688 1.25 3.349.948.1-.747.402-1.25.733-1.538-2.559-.287-5.232-1.279-5.232-5.678 0-1.25.445-2.285 1.178-3.09-.115-.288-.517-1.467.115-3.048 0 0 .963-.302 3.163 1.179.92-.259 1.897-.388 2.875-.388.977 0 1.955.13 2.875.388 2.2-1.495 3.162-1.179 3.162-1.179.633 1.581.23 2.76.115 3.048.733.805 1.179 1.825 1.179 3.09 0 4.413-2.688 5.39-5.247 5.678.417.36.776 1.05.776 2.128 0 1.538-.014 2.774-.014 3.162 0 .302.216.662.79.547C20.709 21.637 24 17.324 24 12.25 24 5.896 18.854.75 12.5.75Z"></path></svg> <span class="show-whenWide">GitHub 中文社区</span></a></div><div class="mr-3"><!--$!--><template data-dgst="BAILOUT_TO_CLIENT_SIDE_RENDERING"></template><!--/$--></div><div class="mr-3 show-whenWide"><a class="Header-link" href="https://forum.github-zh.com">论坛</a></div><div class="mr-3 show-whenWide"><a class="Header-link" href="/top">排行榜</a></div><div class="mr-3 show-whenWide Header-item--full"><a class="Header-link" href="/trending">趋势</a></div><div class="mr-3 show-whenWide"><a class="Header-link" href="/auth/github">登录</a></div></div><!--$--><div style="display:flex"><aside style="background:#fff" class="border-right show-whenWide"><div class="sticky top-0"><br/><nav-list class="px-1" style="width:200px"><a class="no-underline d-block color-fg-default" style="margin-top:10px" href="/trending"><button class="btn btn-block btn-invisible color-fg-default text-left"><svg aria-hidden="true" focusable="false" class="octicon octicon-flame" viewBox="0 0 16 16" width="16" height="16" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M9.533.753V.752c.217 2.385 1.463 3.626 2.653 4.81C13.37 6.74 14.498 7.863 14.498 10c0 3.5-3 6-6.5 6S1.5 13.512 1.5 10c0-1.298.536-2.56 1.425-3.286.376-.308.862 0 1.035.454C4.46 8.487 5.581 8.419 6 8c.282-.282.341-.811-.003-1.5C4.34 3.187 7.035.75 8.77.146c.39-.137.726.194.763.607ZM7.998 14.5c2.832 0 5-1.98 5-4.5 0-1.463-.68-2.19-1.879-3.383l-.036-.037c-1.013-1.008-2.3-2.29-2.834-4.434-.322.256-.63.579-.864.953-.432.696-.621 1.58-.046 2.73.473.947.67 2.284-.278 3.232-.61.61-1.545.84-2.403.633a2.79 2.79 0 0 1-1.436-.874A3.198 3.198 0 0 0 3 10c0 2.53 2.164 4.5 4.998 4.5Z"></path></svg><span class="ml-2 font-sans text-gray-600">趋势</span></button></a><a class="no-underline d-block color-fg-default" style="margin-top:10px" href="/awesome"><button class="btn btn-block btn-invisible color-fg-default text-left"><svg aria-hidden="true" focusable="false" class="octicon octicon-thumbsup" viewBox="0 0 16 16" width="16" height="16" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M8.347.631A.75.75 0 0 1 9.123.26l.238.04a3.25 3.25 0 0 1 2.591 4.098L11.494 6h.665a3.25 3.25 0 0 1 3.118 4.167l-1.135 3.859A2.751 2.751 0 0 1 11.503 16H6.586a3.75 3.75 0 0 1-2.184-.702A1.75 1.75 0 0 1 3 16H1.75A1.75 1.75 0 0 1 0 14.25v-6.5C0 6.784.784 6 1.75 6h3.417a.25.25 0 0 0 .217-.127ZM4.75 13.649l.396.33c.404.337.914.521 1.44.521h4.917a1.25 1.25 0 0 0 1.2-.897l1.135-3.859A1.75 1.75 0 0 0 12.159 7.5H10.5a.75.75 0 0 1-.721-.956l.731-2.558a1.75 1.75 0 0 0-1.127-2.14L6.69 6.611a1.75 1.75 0 0 1-1.523.889H4.75ZM3.25 7.5h-1.5a.25.25 0 0 0-.25.25v6.5c0 .138.112.25.25.25H3a.25.25 0 0 0 .25-.25Z"></path></svg><span class="ml-2 font-sans text-gray-600">精选</span></button></a><a class="no-underline d-block color-fg-default" style="margin-top:10px" href="/top"><button class="btn btn-block btn-invisible color-fg-default text-left"><svg aria-hidden="true" focusable="false" class="octicon octicon-trophy" viewBox="0 0 16 16" width="16" height="16" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M3.217 6.962A3.75 3.75 0 0 1 0 3.25v-.5C0 1.784.784 1 1.75 1h1.356c.228-.585.796-1 1.462-1h6.864c.647 0 1.227.397 1.462 1h1.356c.966 0 1.75.784 1.75 1.75v.5a3.75 3.75 0 0 1-3.217 3.712 5.014 5.014 0 0 1-2.771 3.117l.144 1.446c.005.05.03.12.114.204.086.087.217.17.373.227.283.103.618.274.89.568.285.31.467.723.467 1.226v.75h1.25a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1 0-1.5H4v-.75c0-.503.182-.916.468-1.226.27-.294.606-.465.889-.568.139-.048.266-.126.373-.227.084-.085.109-.153.114-.204l.144-1.446a5.015 5.015 0 0 1-2.77-3.117ZM4.5 1.568V5.5a3.5 3.5 0 1 0 7 0V1.568a.068.068 0 0 0-.068-.068H4.568a.068.068 0 0 0-.068.068Zm2.957 8.902-.12 1.204c-.093.925-.858 1.47-1.467 1.691a.766.766 0 0 0-.3.176c-.037.04-.07.093-.07.21v.75h5v-.75c0-.117-.033-.17-.07-.21a.766.766 0 0 0-.3-.176c-.609-.221-1.374-.766-1.466-1.69l-.12-1.204a5.064 5.064 0 0 1-1.087 0ZM13 2.5v2.872a2.25 2.25 0 0 0 1.5-2.122v-.5a.25.25 0 0 0-.25-.25H13Zm-10 0H1.75a.25.25 0 0 0-.25.25v.5c0 .98.626 1.813 1.5 2.122Z"></path></svg><span class="ml-2 font-sans text-gray-600">排行榜</span></button></a><a class="no-underline d-block color-fg-default" style="margin-top:10px" href="/collections"><button class="btn btn-block btn-invisible color-fg-default text-left"><svg aria-hidden="true" focusable="false" class="octicon octicon-workflow" viewBox="0 0 16 16" width="16" height="16" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M0 1.75C0 .784.784 0 1.75 0h3.5C6.216 0 7 .784 7 1.75v3.5A1.75 1.75 0 0 1 5.25 7H4v4a1 1 0 0 0 1 1h4v-1.25C9 9.784 9.784 9 10.75 9h3.5c.966 0 1.75.784 1.75 1.75v3.5A1.75 1.75 0 0 1 14.25 16h-3.5A1.75 1.75 0 0 1 9 14.25v-.75H5A2.5 2.5 0 0 1 2.5 11V7h-.75A1.75 1.75 0 0 1 0 5.25Zm1.75-.25a.25.25 0 0 0-.25.25v3.5c0 .138.112.25.25.25h3.5a.25.25 0 0 0 .25-.25v-3.5a.25.25 0 0 0-.25-.25Zm9 9a.25.25 0 0 0-.25.25v3.5c0 .138.112.25.25.25h3.5a.25.25 0 0 0 .25-.25v-3.5a.25.25 0 0 0-.25-.25Z"></path></svg><span class="ml-2 font-sans text-gray-600">索引</span></button></a><a class="no-underline d-block color-fg-default" style="margin-top:10px" href="/search"><button class="btn btn-block btn-invisible color-fg-default text-left"><svg aria-hidden="true" focusable="false" class="octicon octicon-search" viewBox="0 0 16 16" width="16" height="16" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M10.68 11.74a6 6 0 0 1-7.922-8.982 6 6 0 0 1 8.982 7.922l3.04 3.04a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215ZM11.5 7a4.499 4.499 0 1 0-8.997 0A4.499 4.499 0 0 0 11.5 7Z"></path></svg><span class="ml-2 font-sans text-gray-600">搜索</span></button></a><a class="no-underline d-block color-fg-default" style="margin-top:10px" href="/real-world"><button class="btn btn-block btn-invisible color-fg-default text-left"><svg aria-hidden="true" focusable="false" class="octicon octicon-star" viewBox="0 0 16 16" width="16" height="16" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M8 .25a.75.75 0 0 1 .673.418l1.882 3.815 4.21.612a.75.75 0 0 1 .416 1.279l-3.046 2.97.719 4.192a.751.751 0 0 1-1.088.791L8 12.347l-3.766 1.98a.75.75 0 0 1-1.088-.79l.72-4.194L.818 6.374a.75.75 0 0 1 .416-1.28l4.21-.611L7.327.668A.75.75 0 0 1 8 .25Zm0 2.445L6.615 5.5a.75.75 0 0 1-.564.41l-3.097.45 2.24 2.184a.75.75 0 0 1 .216.664l-.528 3.084 2.769-1.456a.75.75 0 0 1 .698 0l2.77 1.456-.53-3.084a.75.75 0 0 1 .216-.664l2.24-2.183-3.096-.45a.75.75 0 0 1-.564-.41L8 2.694Z"></path></svg><span class="ml-2 font-sans text-gray-600">实战项目</span></button></a><a class="no-underline d-block color-fg-default" style="margin-top:10px" href="/top-developers"><button class="btn btn-block btn-invisible color-fg-default text-left"><svg aria-hidden="true" focusable="false" class="octicon octicon-trophy" viewBox="0 0 16 16" width="16" height="16" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M3.217 6.962A3.75 3.75 0 0 1 0 3.25v-.5C0 1.784.784 1 1.75 1h1.356c.228-.585.796-1 1.462-1h6.864c.647 0 1.227.397 1.462 1h1.356c.966 0 1.75.784 1.75 1.75v.5a3.75 3.75 0 0 1-3.217 3.712 5.014 5.014 0 0 1-2.771 3.117l.144 1.446c.005.05.03.12.114.204.086.087.217.17.373.227.283.103.618.274.89.568.285.31.467.723.467 1.226v.75h1.25a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1 0-1.5H4v-.75c0-.503.182-.916.468-1.226.27-.294.606-.465.889-.568.139-.048.266-.126.373-.227.084-.085.109-.153.114-.204l.144-1.446a5.015 5.015 0 0 1-2.77-3.117ZM4.5 1.568V5.5a3.5 3.5 0 1 0 7 0V1.568a.068.068 0 0 0-.068-.068H4.568a.068.068 0 0 0-.068.068Zm2.957 8.902-.12 1.204c-.093.925-.858 1.47-1.467 1.691a.766.766 0 0 0-.3.176c-.037.04-.07.093-.07.21v.75h5v-.75c0-.117-.033-.17-.07-.21a.766.766 0 0 0-.3-.176c-.609-.221-1.374-.766-1.466-1.69l-.12-1.204a5.064 5.064 0 0 1-1.087 0ZM13 2.5v2.872a2.25 2.25 0 0 0 1.5-2.122v-.5a.25.25 0 0 0-.25-.25H13Zm-10 0H1.75a.25.25 0 0 0-.25.25v.5c0 .98.626 1.813 1.5 2.122Z"></path></svg><span class="ml-2 font-sans text-gray-600">开发者榜单</span></button></a></nav-list></div></aside><div class="flex-auto"><section style="background:#f6f8fa" class="pb-4"><div><div class="border-bottom mb-6" style="background:#fff"><div class="container-lg p-responsive text-center py-6"><h1 class="h3">GitHub 中文社区</h1><p class="color-fg-subtle col-md-8 mx-auto">GitHub 是世界上最大的代码托管平台，超 1 亿开发者正在使用。GitHub 中文社区是一个致力于分享和传播 GitHub 上优质开源项目的中文社区平台。</p></div></div><div class="md:container lg:container mx-auto pt-0 mt-0"><div class="flex flex-row justify-center"><div class="basis-full lg:basis-9/12"><div class="p-1 grid grid-cols-2 md:grid-cols-3 gap-1"><div><div class="Box anim-hover-grow p-3 color-fg-accent" style="height:100%"><a style="display:block;text-decoration:none" href="/getting-started"><p class="f4 text-center">新手训练营</p><p class="f5 text-center color-fg-subtle mb-0">适合所有人的交互式课程</p></a></div></div><div><div class="Box anim-hover-grow p-3 color-fg-accent" style="height:100%"><a style="display:block;text-decoration:none" href="/trending"><p class="f4 text-center">GitHub 趋势</p><p class="f5 text-center color-fg-subtle mb-0">查看当前流行和历史趋势</p></a></div></div><div><div class="Box anim-hover-grow p-3 color-fg-accent" style="height:100%"><a style="display:block;text-decoration:none" href="/top"><p class="f4 text-center">排行榜</p><p class="f5 text-center color-fg-subtle mb-0">查看 GitHub 上最受欢迎的开源项目</p></a></div></div><div><div class="Box anim-hover-grow p-3 color-fg-accent" style="height:100%"><a style="display:block;text-decoration:none" href="/awesome"><p class="f4 text-center">GitHub 精选</p><p class="f5 text-center color-fg-subtle mb-0">精选优质开源项目和学习资源</p></a></div></div><div><div class="Box anim-hover-grow p-3 color-fg-accent" style="height:100%"><a style="display:block;text-decoration:none" href="https://github.com/"><p class="f4 text-center">GitHub 官网</p><p class="f5 text-center color-fg-subtle mb-0">GitHub 英文官网入口</p></a></div></div><div><div class="Box anim-hover-grow p-3 color-fg-accent" style="height:100%"><a style="display:block;text-decoration:none" href="https://forum.github-zh.com/"><p class="f4 text-center">论坛</p><p class="f5 text-center color-fg-subtle mb-0">开发者论坛</p></a></div></div></div></div></div></div></div></section><section style="background:#f6f8fa" class="mb-4"><div class="md:container lg:container mx-auto pt-0 mt-0"><div class="flex flex-row justify-center"><div class="basis-full lg:basis-9/12"><section><div class="Box-sc-g0xbh4-0 fHrHav" aria-label="Title" role="banner"><div class="Box-sc-g0xbh4-0 cpOVOS" data-component="TitleArea" data-size-variant="subtitle"><div class="Box-sc-g0xbh4-0 enysYT" data-component="PH_LeadingVisual"><span><svg aria-hidden="true" focusable="false" class="color-fg-severe" viewBox="0 0 16 16" width="18" height="18" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M9.533.753V.752c.217 2.385 1.463 3.626 2.653 4.81C13.37 6.74 14.498 7.863 14.498 10c0 3.5-3 6-6.5 6S1.5 13.512 1.5 10c0-1.298.536-2.56 1.425-3.286.376-.308.862 0 1.035.454C4.46 8.487 5.581 8.419 6 8c.282-.282.341-.811-.003-1.5C4.34 3.187 7.035.75 8.77.146c.39-.137.726.194.763.607ZM7.998 14.5c2.832 0 5-1.98 5-4.5 0-1.463-.68-2.19-1.879-3.383l-.036-.037c-1.013-1.008-2.3-2.29-2.834-4.434-.322.256-.63.579-.864.953-.432.696-.621 1.58-.046 2.73.473.947.67 2.284-.278 3.232-.61.61-1.545.84-2.403.633a2.79 2.79 0 0 1-1.436-.874A3.198 3.198 0 0 0 3 10c0 2.53 2.164 4.5 4.998 4.5Z"></path></svg></span></div><h2 class="Box-sc-g0xbh4-0 hFAVwB prc-Heading-Heading-6CmGO" data-component="PH_Title"><a class="color-fg-default f4" href="/trending-china">中文趋势</a></h2></div></div><section class="mt-1 mb-5"><div class="Boxz"><div class="color-bg-subtle Box-rowz rounded-1"><div class="grid grid-cols-12 gap-3"><div class="col-span-12 md:col-span-6"><div class="Box" style="display:flex;overflow:hidden;height:100%;cursor:pointer"><div class="repo-card anim-hover-grow"><div style="flex-grow:1"><div class="py-2 px-3 pb-2"><div class="mb-3 flex items-center"><img data-component="Avatar" class="Avatar__StyledAvatar-sc-2lv0r8-0 kRIKrw mr-3" size="35" src="https://static.github-zh.com/github_avatars/JoeanAmier?size=35" height="35" width="35"/><div class="d-flex flex-column"><a style="display:block;width:100%;font-size:18px" rel="nofollow noopener" href="https://github.com/JoeanAmier/TikTokDownloader" class="text-bold color-fg-default">TikTokDownloader</a><div class="flex items-center"><a style="display:flex" rel="nofollow noopener" href="https://github.com/JoeanAmier" class="color-fg-muted"><span style="display:block" class="flex-none truncate">@<!-- -->JoeanAmier</span></a></div></div></div><div class="body"><p class="color-fg-muted mb-0"><span>TikTok &#21457;&#24067;/&#21916;&#27426;/&#21512;&#36753;/&#30452;&#25773;/&#35270;&#39057;/&#22270;&#38598;/&#38899;&#20048;；&#25238;&#38899;&#21457;&#24067;/&#21916;&#27426;/&#25910;&#34255;/&#25910;&#34255;&#22841;/&#35270;&#39057;/&#22270;&#38598;/&#23454;&#20917;/&#30452;&#25773;/&#38899;&#20048;/&#21512;&#38598;/&#35780;&#35770;/&#36134;&#21495;/&#25628;&#32034;/&#28909;&#27036;&#25968;&#25454;&#37319;&#38598;&#24037;&#20855;/&#19979;&#36733;&#24037;&#20855;</span></p></div><div class="d-flex flex-wrap pt-2"><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/douyin">douyin</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/python">Python</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/tiktok">TikTok</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/api">API</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/csv">CSV</a></div></div></div><div class="py-2 px-3"><div style="display:flex;justify-content:space-between;width:100%"><div><span class="text-small mr-2 color-fg-subtle"><span style="background:#3572A5" class="point"></span> <!-- -->Python</span><span class="text-small mr-1 color-fg-subtle"><svg aria-hidden="true" focusable="false" class="octicon octicon-star" viewBox="0 0 16 16" width="14" height="14" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M8 .25a.75.75 0 0 1 .673.418l1.882 3.815 4.21.612a.75.75 0 0 1 .416 1.279l-3.046 2.97.719 4.192a.751.751 0 0 1-1.088.791L8 12.347l-3.766 1.98a.75.75 0 0 1-1.088-.79l.72-4.194L.818 6.374a.75.75 0 0 1 .416-1.28l4.21-.611L7.327.668A.75.75 0 0 1 8 .25Zm0 2.445L6.615 5.5a.75.75 0 0 1-.564.41l-3.097.45 2.24 2.184a.75.75 0 0 1 .216.664l-.528 3.084 2.769-1.456a.75.75 0 0 1 .698 0l2.77 1.456-.53-3.084a.75.75 0 0 1 .216-.664l2.24-2.183-3.096-.45a.75.75 0 0 1-.564-.41L8 2.694Z"></path></svg>11.01 k</span><span class="mx-2 color-fg-muted summary-icon"><svg height="16" stroke-linejoin="round" viewBox="0 0 16 16" width="16" style="color:currentcolor"><path d="M2.5 0.5V0H3.5V0.5C3.5 1.60457 4.39543 2.5 5.5 2.5H6V3V3.5H5.5C4.39543 3.5 3.5 4.39543 3.5 5.5V6H3H2.5V5.5C2.5 4.39543 1.60457 3.5 0.5 3.5H0V3V2.5H0.5C1.60457 2.5 2.5 1.60457 2.5 0.5Z" fill="currentColor"></path><path d="M14.5 4.5V5H13.5V4.5C13.5 3.94772 13.0523 3.5 12.5 3.5H12V3V2.5H12.5C13.0523 2.5 13.5 2.05228 13.5 1.5V1H14H14.5V1.5C14.5 2.05228 14.9477 2.5 15.5 2.5H16V3V3.5H15.5C14.9477 3.5 14.5 3.94772 14.5 4.5Z" fill="currentColor"></path><path d="M8.40706 4.92939L8.5 4H9.5L9.59294 4.92939C9.82973 7.29734 11.7027 9.17027 14.0706 9.40706L15 9.5V10.5L14.0706 10.5929C11.7027 10.8297 9.82973 12.7027 9.59294 15.0706L9.5 16H8.5L8.40706 15.0706C8.17027 12.7027 6.29734 10.8297 3.92939 10.5929L3 10.5V9.5L3.92939 9.40706C6.29734 9.17027 8.17027 7.29734 8.40706 4.92939Z" fill="currentColor"></path></svg></span></div><div class="text-small color-fg-subtle"><span>7 天前</span><span class="ml-2">🇨🇳</span></div></div></div></div></div></div><div class="col-span-12 md:col-span-6"><div class="Box" style="display:flex;overflow:hidden;height:100%;cursor:pointer"><div class="repo-card anim-hover-grow"><div style="flex-grow:1"><div class="py-2 px-3 pb-2"><div class="mb-3 flex items-center"><img data-component="Avatar" class="Avatar__StyledAvatar-sc-2lv0r8-0 kRIKrw mr-3" size="35" src="https://static.github-zh.com/github_avatars/OI-wiki?size=35" height="35" width="35"/><div class="d-flex flex-column"><a style="display:block;width:100%;font-size:18px" rel="nofollow noopener" href="https://github.com/OI-wiki/OI-wiki" class="text-bold color-fg-default">OI-wiki</a><div class="flex items-center"><a style="display:flex" rel="nofollow noopener" href="https://github.com/OI-wiki" class="color-fg-muted"><span style="display:block" class="flex-none truncate">@<!-- -->OI-wiki</span></a></div></div></div><div class="body"><p class="color-fg-muted mb-0"><a class="mr-2" style="font-size:95%" href="/collections/algorithm">#<!-- -->算法刷题<!-- -->#</a><span>🌟 Wiki of OI / ICPC for everyone. （&#26576;&#22823;&#22411;&#28216;&#25103;&#32447;&#19978;&#25915;&#30053;，&#20869;&#21547;&#28843;&#37239;&#31639;&#26415;魔&#27861;）</span></p></div><div class="d-flex flex-wrap pt-2"><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/oi">oi</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/oi-handbook">oi-handbook</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/data-structures">数据结构</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/algorithms">算法</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/competitive-programming">competitive-programming</a></div></div></div><div class="py-2 px-3"><div style="display:flex;justify-content:space-between;width:100%"><div><span class="text-small mr-2 color-fg-subtle"><span style="background:#3178c6" class="point"></span> <!-- -->TypeScript</span><span class="text-small mr-1 color-fg-subtle"><svg aria-hidden="true" focusable="false" class="octicon octicon-star" viewBox="0 0 16 16" width="14" height="14" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M8 .25a.75.75 0 0 1 .673.418l1.882 3.815 4.21.612a.75.75 0 0 1 .416 1.279l-3.046 2.97.719 4.192a.751.751 0 0 1-1.088.791L8 12.347l-3.766 1.98a.75.75 0 0 1-1.088-.79l.72-4.194L.818 6.374a.75.75 0 0 1 .416-1.28l4.21-.611L7.327.668A.75.75 0 0 1 8 .25Zm0 2.445L6.615 5.5a.75.75 0 0 1-.564.41l-3.097.45 2.24 2.184a.75.75 0 0 1 .216.664l-.528 3.084 2.769-1.456a.75.75 0 0 1 .698 0l2.77 1.456-.53-3.084a.75.75 0 0 1 .216-.664l2.24-2.183-3.096-.45a.75.75 0 0 1-.564-.41L8 2.694Z"></path></svg>23.72 k</span><span class="mx-2 color-fg-muted summary-icon"><svg height="16" stroke-linejoin="round" viewBox="0 0 16 16" width="16" style="color:currentcolor"><path d="M2.5 0.5V0H3.5V0.5C3.5 1.60457 4.39543 2.5 5.5 2.5H6V3V3.5H5.5C4.39543 3.5 3.5 4.39543 3.5 5.5V6H3H2.5V5.5C2.5 4.39543 1.60457 3.5 0.5 3.5H0V3V2.5H0.5C1.60457 2.5 2.5 1.60457 2.5 0.5Z" fill="currentColor"></path><path d="M14.5 4.5V5H13.5V4.5C13.5 3.94772 13.0523 3.5 12.5 3.5H12V3V2.5H12.5C13.0523 2.5 13.5 2.05228 13.5 1.5V1H14H14.5V1.5C14.5 2.05228 14.9477 2.5 15.5 2.5H16V3V3.5H15.5C14.9477 3.5 14.5 3.94772 14.5 4.5Z" fill="currentColor"></path><path d="M8.40706 4.92939L8.5 4H9.5L9.59294 4.92939C9.82973 7.29734 11.7027 9.17027 14.0706 9.40706L15 9.5V10.5L14.0706 10.5929C11.7027 10.8297 9.82973 12.7027 9.59294 15.0706L9.5 16H8.5L8.40706 15.0706C8.17027 12.7027 6.29734 10.8297 3.92939 10.5929L3 10.5V9.5L3.92939 9.40706C6.29734 9.17027 8.17027 7.29734 8.40706 4.92939Z" fill="currentColor"></path></svg></span></div><div class="text-small color-fg-subtle"><span>8 分钟前</span><span class="ml-2">🇨🇳</span></div></div></div></div></div></div><div class="col-span-12 md:col-span-6"><div class="Box" style="display:flex;overflow:hidden;height:100%;cursor:pointer"><div class="repo-card anim-hover-grow"><div style="flex-grow:1"><div class="py-2 px-3 pb-2"><div class="mb-3 flex items-center"><img data-component="Avatar" class="Avatar__StyledAvatar-sc-2lv0r8-0 kRIKrw mr-3" size="35" src="https://static.github-zh.com/github_avatars/XayahSuSuSu?size=35" height="35" width="35"/><div class="d-flex flex-column"><a style="display:block;width:100%;font-size:18px" rel="nofollow noopener" href="https://github.com/XayahSuSuSu/Android-DataBackup" class="text-bold color-fg-default">Android-DataBackup</a><div class="flex items-center"><a style="display:flex" rel="nofollow noopener" href="https://github.com/XayahSuSuSu" class="color-fg-muted"><span style="display:block" class="flex-none truncate">@<!-- -->XayahSuSuSu</span></a></div></div></div><div class="body"><p class="color-fg-muted mb-0"><a class="mr-2" style="font-size:95%" href="/collections/android">#<!-- -->安卓<!-- -->#</a><span>&#19968;&#27454;&#36816;&#34892;&#22312;Android 8+&#30340;&#20813;&#36153;&#24320;&#28304;&#30340;&#25968;&#25454;&#22791;&#20221;&#24212;&#29992;</span></p></div><div class="d-flex flex-wrap pt-2"><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/backup">backup</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/root">root</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/android">Android</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/zstd">zstd</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/compose">compose</a></div></div></div><div class="py-2 px-3"><div style="display:flex;justify-content:space-between;width:100%"><div><span class="text-small mr-2 color-fg-subtle"><span style="background:#A97BFF" class="point"></span> <!-- -->Kotlin</span><span class="text-small mr-1 color-fg-subtle"><svg aria-hidden="true" focusable="false" class="octicon octicon-star" viewBox="0 0 16 16" width="14" height="14" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M8 .25a.75.75 0 0 1 .673.418l1.882 3.815 4.21.612a.75.75 0 0 1 .416 1.279l-3.046 2.97.719 4.192a.751.751 0 0 1-1.088.791L8 12.347l-3.766 1.98a.75.75 0 0 1-1.088-.79l.72-4.194L.818 6.374a.75.75 0 0 1 .416-1.28l4.21-.611L7.327.668A.75.75 0 0 1 8 .25Zm0 2.445L6.615 5.5a.75.75 0 0 1-.564.41l-3.097.45 2.24 2.184a.75.75 0 0 1 .216.664l-.528 3.084 2.769-1.456a.75.75 0 0 1 .698 0l2.77 1.456-.53-3.084a.75.75 0 0 1 .216-.664l2.24-2.183-3.096-.45a.75.75 0 0 1-.564-.41L8 2.694Z"></path></svg>5.31 k</span><span class="mx-2 color-fg-muted summary-icon"><svg height="16" stroke-linejoin="round" viewBox="0 0 16 16" width="16" style="color:currentcolor"><path d="M2.5 0.5V0H3.5V0.5C3.5 1.60457 4.39543 2.5 5.5 2.5H6V3V3.5H5.5C4.39543 3.5 3.5 4.39543 3.5 5.5V6H3H2.5V5.5C2.5 4.39543 1.60457 3.5 0.5 3.5H0V3V2.5H0.5C1.60457 2.5 2.5 1.60457 2.5 0.5Z" fill="currentColor"></path><path d="M14.5 4.5V5H13.5V4.5C13.5 3.94772 13.0523 3.5 12.5 3.5H12V3V2.5H12.5C13.0523 2.5 13.5 2.05228 13.5 1.5V1H14H14.5V1.5C14.5 2.05228 14.9477 2.5 15.5 2.5H16V3V3.5H15.5C14.9477 3.5 14.5 3.94772 14.5 4.5Z" fill="currentColor"></path><path d="M8.40706 4.92939L8.5 4H9.5L9.59294 4.92939C9.82973 7.29734 11.7027 9.17027 14.0706 9.40706L15 9.5V10.5L14.0706 10.5929C11.7027 10.8297 9.82973 12.7027 9.59294 15.0706L9.5 16H8.5L8.40706 15.0706C8.17027 12.7027 6.29734 10.8297 3.92939 10.5929L3 10.5V9.5L3.92939 9.40706C6.29734 9.17027 8.17027 7.29734 8.40706 4.92939Z" fill="currentColor"></path></svg></span></div><div class="text-small color-fg-subtle"><span>3 天前</span><span class="ml-2">🇨🇳</span></div></div></div></div></div></div><div class="col-span-12 md:col-span-6"><div class="Box" style="display:flex;overflow:hidden;height:100%;cursor:pointer"><div class="repo-card anim-hover-grow"><div style="flex-grow:1"><div class="py-2 px-3 pb-2"><div class="mb-3 flex items-center"><img data-component="Avatar" class="Avatar__StyledAvatar-sc-2lv0r8-0 kRIKrw mr-3" size="35" src="https://static.github-zh.com/github_avatars/MatsuriDayo?size=35" height="35" width="35"/><div class="d-flex flex-column"><a style="display:block;width:100%;font-size:18px" rel="nofollow noopener" href="https://github.com/MatsuriDayo/nekoray" class="text-bold color-fg-default">nekoray<span class="Label Label--attention v-align-middle text-xs ml-2 mt-1 no-wrap v-align-baseline Label--inline">存档</span></a><div class="flex items-center"><a style="display:flex" rel="nofollow noopener" href="https://github.com/MatsuriDayo" class="color-fg-muted"><span style="display:block" class="flex-none truncate">@<!-- -->MatsuriDayo</span></a></div></div></div><div class="body"><p class="color-fg-muted mb-0"><span>&#19981;&#20877;&#32500;&#25252;，&#33258;&#23547;&#26367;&#20195;&#21697;&#12290; Qt based cross-platform GUI proxy configuration manager (backend: sing-box)</span></p></div><div class="d-flex flex-wrap pt-2"></div></div></div><div class="py-2 px-3"><div style="display:flex;justify-content:space-between;width:100%"><div><span class="text-small mr-2 color-fg-subtle"><span style="background:#f34b7d" class="point"></span> <!-- -->C++</span><span class="text-small mr-1 color-fg-subtle"><svg aria-hidden="true" focusable="false" class="octicon octicon-star" viewBox="0 0 16 16" width="14" height="14" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M8 .25a.75.75 0 0 1 .673.418l1.882 3.815 4.21.612a.75.75 0 0 1 .416 1.279l-3.046 2.97.719 4.192a.751.751 0 0 1-1.088.791L8 12.347l-3.766 1.98a.75.75 0 0 1-1.088-.79l.72-4.194L.818 6.374a.75.75 0 0 1 .416-1.28l4.21-.611L7.327.668A.75.75 0 0 1 8 .25Zm0 2.445L6.615 5.5a.75.75 0 0 1-.564.41l-3.097.45 2.24 2.184a.75.75 0 0 1 .216.664l-.528 3.084 2.769-1.456a.75.75 0 0 1 .698 0l2.77 1.456-.53-3.084a.75.75 0 0 1 .216-.664l2.24-2.183-3.096-.45a.75.75 0 0 1-.564-.41L8 2.694Z"></path></svg>14.8 k</span><span class="mx-2 color-fg-muted summary-icon"><svg height="16" stroke-linejoin="round" viewBox="0 0 16 16" width="16" style="color:currentcolor"><path d="M2.5 0.5V0H3.5V0.5C3.5 1.60457 4.39543 2.5 5.5 2.5H6V3V3.5H5.5C4.39543 3.5 3.5 4.39543 3.5 5.5V6H3H2.5V5.5C2.5 4.39543 1.60457 3.5 0.5 3.5H0V3V2.5H0.5C1.60457 2.5 2.5 1.60457 2.5 0.5Z" fill="currentColor"></path><path d="M14.5 4.5V5H13.5V4.5C13.5 3.94772 13.0523 3.5 12.5 3.5H12V3V2.5H12.5C13.0523 2.5 13.5 2.05228 13.5 1.5V1H14H14.5V1.5C14.5 2.05228 14.9477 2.5 15.5 2.5H16V3V3.5H15.5C14.9477 3.5 14.5 3.94772 14.5 4.5Z" fill="currentColor"></path><path d="M8.40706 4.92939L8.5 4H9.5L9.59294 4.92939C9.82973 7.29734 11.7027 9.17027 14.0706 9.40706L15 9.5V10.5L14.0706 10.5929C11.7027 10.8297 9.82973 12.7027 9.59294 15.0706L9.5 16H8.5L8.40706 15.0706C8.17027 12.7027 6.29734 10.8297 3.92939 10.5929L3 10.5V9.5L3.92939 9.40706C6.29734 9.17027 8.17027 7.29734 8.40706 4.92939Z" fill="currentColor"></path></svg></span></div><div class="text-small color-fg-subtle"><span>8 个月前</span><span class="ml-2">🇨🇳</span></div></div></div></div></div></div><div class="col-span-12 md:col-span-6"><div class="Box" style="display:flex;overflow:hidden;height:100%;cursor:pointer"><div class="repo-card anim-hover-grow"><div style="flex-grow:1"><div class="py-2 px-3 pb-2"><div class="mb-3 flex items-center"><img data-component="Avatar" class="Avatar__StyledAvatar-sc-2lv0r8-0 kRIKrw mr-3" size="35" src="https://static.github-zh.com/github_avatars/fanmingming?size=35" height="35" width="35"/><div class="d-flex flex-column"><a style="display:block;width:100%;font-size:18px" rel="nofollow noopener" href="https://github.com/fanmingming/live" class="text-bold color-fg-default">live</a><div class="flex items-center"><a style="display:flex" rel="nofollow noopener" href="https://github.com/fanmingming" class="color-fg-muted"><span style="display:block" class="flex-none truncate">@<!-- -->fanmingming</span></a></div></div></div><div class="body"><p class="color-fg-muted mb-0"><a class="mr-2" style="font-size:95%" href="/collections/iptv">#<!-- -->IPTV<!-- -->#</a><span>&#10031; &#21487;&#30452;&#36830;&#35775;&#38382;&#30340;&#30005;&#35270;/&#24191;&#25773;&#22270;&#26631;&#24211;&#19982;&#30456;&#20851;&#24037;&#20855;&#39033;&#30446; &#10031; 🔕 &#27704;&#20037;&#20813;&#36153; &#30452;&#36830;&#35775;&#38382; &#23436;&#25972;&#24320;&#28304; &#19981;&#26029;&#23436;&#21892;&#30340;&#21488;&#26631; &#25903;&#25345;IPv4/IPv6&#21452;&#26632;&#35775;&#38382; 🔕</span></p></div><div class="d-flex flex-wrap pt-2"><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/radio">radio</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/iptv">iptv</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/ipv6">ipv6</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/live">live</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/m3u">m3u</a></div></div></div><div class="py-2 px-3"><div style="display:flex;justify-content:space-between;width:100%"><div><span class="text-small mr-2 color-fg-subtle"><span style="background:#f1e05a" class="point"></span> <!-- -->JavaScript</span><span class="text-small mr-1 color-fg-subtle"><svg aria-hidden="true" focusable="false" class="octicon octicon-star" viewBox="0 0 16 16" width="14" height="14" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M8 .25a.75.75 0 0 1 .673.418l1.882 3.815 4.21.612a.75.75 0 0 1 .416 1.279l-3.046 2.97.719 4.192a.751.751 0 0 1-1.088.791L8 12.347l-3.766 1.98a.75.75 0 0 1-1.088-.79l.72-4.194L.818 6.374a.75.75 0 0 1 .416-1.28l4.21-.611L7.327.668A.75.75 0 0 1 8 .25Zm0 2.445L6.615 5.5a.75.75 0 0 1-.564.41l-3.097.45 2.24 2.184a.75.75 0 0 1 .216.664l-.528 3.084 2.769-1.456a.75.75 0 0 1 .698 0l2.77 1.456-.53-3.084a.75.75 0 0 1 .216-.664l2.24-2.183-3.096-.45a.75.75 0 0 1-.564-.41L8 2.694Z"></path></svg>26.07 k</span><span class="mx-2 color-fg-muted summary-icon"><svg height="16" stroke-linejoin="round" viewBox="0 0 16 16" width="16" style="color:currentcolor"><path d="M2.5 0.5V0H3.5V0.5C3.5 1.60457 4.39543 2.5 5.5 2.5H6V3V3.5H5.5C4.39543 3.5 3.5 4.39543 3.5 5.5V6H3H2.5V5.5C2.5 4.39543 1.60457 3.5 0.5 3.5H0V3V2.5H0.5C1.60457 2.5 2.5 1.60457 2.5 0.5Z" fill="currentColor"></path><path d="M14.5 4.5V5H13.5V4.5C13.5 3.94772 13.0523 3.5 12.5 3.5H12V3V2.5H12.5C13.0523 2.5 13.5 2.05228 13.5 1.5V1H14H14.5V1.5C14.5 2.05228 14.9477 2.5 15.5 2.5H16V3V3.5H15.5C14.9477 3.5 14.5 3.94772 14.5 4.5Z" fill="currentColor"></path><path d="M8.40706 4.92939L8.5 4H9.5L9.59294 4.92939C9.82973 7.29734 11.7027 9.17027 14.0706 9.40706L15 9.5V10.5L14.0706 10.5929C11.7027 10.8297 9.82973 12.7027 9.59294 15.0706L9.5 16H8.5L8.40706 15.0706C8.17027 12.7027 6.29734 10.8297 3.92939 10.5929L3 10.5V9.5L3.92939 9.40706C6.29734 9.17027 8.17027 7.29734 8.40706 4.92939Z" fill="currentColor"></path></svg></span></div><div class="text-small color-fg-subtle"><span>2 小时前</span><span class="ml-2">🇨🇳</span></div></div></div></div></div></div><div class="col-span-12 md:col-span-6"><div class="Box" style="display:flex;overflow:hidden;height:100%;cursor:pointer"><div class="repo-card anim-hover-grow"><div style="flex-grow:1"><div class="py-2 px-3 pb-2"><div class="mb-3 flex items-center"><img data-component="Avatar" class="Avatar__StyledAvatar-sc-2lv0r8-0 kRIKrw mr-3" alt="coolsnowwolf" size="35" src="https://static.github-zh.com/github_avatars/coolsnowwolf?size=35" height="35" width="35"/><div class="d-flex flex-column"><a style="display:block;width:100%;font-size:18px" rel="nofollow noopener" href="https://github.com/coolsnowwolf/lede" class="text-bold color-fg-default">lede</a><div class="flex items-center"><a style="display:flex" rel="nofollow noopener" href="https://github.com/coolsnowwolf" class="color-fg-muted"><span style="display:block" class="truncate shrink">coolsnowwolf</span><span style="display:block" class="flex-none truncate">@<!-- -->coolsnowwolf</span></a></div></div></div><div class="body"><p class="color-fg-muted mb-0"><span>&#22914;&#20309;&#32534;&#35793;&#33258;&#24049;&#38656;&#35201;&#30340; OpenWrt &#22266;&#20214;</span></p></div><div class="d-flex flex-wrap pt-2"><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/lede">lede</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/lua">Lua</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/openwrt-package">openwrt-package</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/openwrt-zh-cn">openwrt-zh-cn</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/openwrt-feed">openwrt-feed</a></div></div></div><div class="py-2 px-3"><div style="display:flex;justify-content:space-between;width:100%"><div><span class="text-small mr-2 color-fg-subtle"><span style="background:#555555" class="point"></span> <!-- -->C</span><span class="text-small mr-1 color-fg-subtle"><svg aria-hidden="true" focusable="false" class="octicon octicon-star" viewBox="0 0 16 16" width="14" height="14" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M8 .25a.75.75 0 0 1 .673.418l1.882 3.815 4.21.612a.75.75 0 0 1 .416 1.279l-3.046 2.97.719 4.192a.751.751 0 0 1-1.088.791L8 12.347l-3.766 1.98a.75.75 0 0 1-1.088-.79l.72-4.194L.818 6.374a.75.75 0 0 1 .416-1.28l4.21-.611L7.327.668A.75.75 0 0 1 8 .25Zm0 2.445L6.615 5.5a.75.75 0 0 1-.564.41l-3.097.45 2.24 2.184a.75.75 0 0 1 .216.664l-.528 3.084 2.769-1.456a.75.75 0 0 1 .698 0l2.77 1.456-.53-3.084a.75.75 0 0 1 .216-.664l2.24-2.183-3.096-.45a.75.75 0 0 1-.564-.41L8 2.694Z"></path></svg>30.87 k</span><span class="mx-2 color-fg-muted summary-icon"><svg height="16" stroke-linejoin="round" viewBox="0 0 16 16" width="16" style="color:currentcolor"><path d="M2.5 0.5V0H3.5V0.5C3.5 1.60457 4.39543 2.5 5.5 2.5H6V3V3.5H5.5C4.39543 3.5 3.5 4.39543 3.5 5.5V6H3H2.5V5.5C2.5 4.39543 1.60457 3.5 0.5 3.5H0V3V2.5H0.5C1.60457 2.5 2.5 1.60457 2.5 0.5Z" fill="currentColor"></path><path d="M14.5 4.5V5H13.5V4.5C13.5 3.94772 13.0523 3.5 12.5 3.5H12V3V2.5H12.5C13.0523 2.5 13.5 2.05228 13.5 1.5V1H14H14.5V1.5C14.5 2.05228 14.9477 2.5 15.5 2.5H16V3V3.5H15.5C14.9477 3.5 14.5 3.94772 14.5 4.5Z" fill="currentColor"></path><path d="M8.40706 4.92939L8.5 4H9.5L9.59294 4.92939C9.82973 7.29734 11.7027 9.17027 14.0706 9.40706L15 9.5V10.5L14.0706 10.5929C11.7027 10.8297 9.82973 12.7027 9.59294 15.0706L9.5 16H8.5L8.40706 15.0706C8.17027 12.7027 6.29734 10.8297 3.92939 10.5929L3 10.5V9.5L3.92939 9.40706C6.29734 9.17027 8.17027 7.29734 8.40706 4.92939Z" fill="currentColor"></path></svg></span></div><div class="text-small color-fg-subtle"><span>17 小时前</span><span class="ml-2">🇨🇳</span></div></div></div></div></div></div></div></div></div><div style="text-align:center;margin:10px 20px"><button type="button" class="types__StyledButton-sc-ws60qy-0 ctABYc" data-loading="false" data-no-visuals="true" data-size="small" aria-describedby=":R3ol7pjdb:-loading-announcement"><span data-component="buttonContent" class="Box-sc-g0xbh4-0 gUkoLg"><span data-component="text">展开 <svg aria-hidden="true" focusable="false" class="octicon octicon-arrow-down" viewBox="0 0 16 16" width="15" height="15" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M13.03 8.22a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L3.47 9.28a.751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018l2.97 2.97V3.75a.75.75 0 0 1 1.5 0v7.44l2.97-2.97a.75.75 0 0 1 1.06 0Z"></path></svg></span></span></button></div></section></section><noscript></noscript><section><div class="Box-sc-g0xbh4-0 fHrHav" aria-label="Title" role="banner"><div class="Box-sc-g0xbh4-0 cpOVOS" data-component="TitleArea" data-size-variant="subtitle"><div class="Box-sc-g0xbh4-0 enysYT" data-component="PH_LeadingVisual"><span><svg aria-hidden="true" focusable="false" class="color-fg-severe" viewBox="0 0 16 16" width="18" height="18" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M9.533.753V.752c.217 2.385 1.463 3.626 2.653 4.81C13.37 6.74 14.498 7.863 14.498 10c0 3.5-3 6-6.5 6S1.5 13.512 1.5 10c0-1.298.536-2.56 1.425-3.286.376-.308.862 0 1.035.454C4.46 8.487 5.581 8.419 6 8c.282-.282.341-.811-.003-1.5C4.34 3.187 7.035.75 8.77.146c.39-.137.726.194.763.607ZM7.998 14.5c2.832 0 5-1.98 5-4.5 0-1.463-.68-2.19-1.879-3.383l-.036-.037c-1.013-1.008-2.3-2.29-2.834-4.434-.322.256-.63.579-.864.953-.432.696-.621 1.58-.046 2.73.473.947.67 2.284-.278 3.232-.61.61-1.545.84-2.403.633a2.79 2.79 0 0 1-1.436-.874A3.198 3.198 0 0 0 3 10c0 2.53 2.164 4.5 4.998 4.5Z"></path></svg></span></div><h2 class="Box-sc-g0xbh4-0 hFAVwB prc-Heading-Heading-6CmGO" data-component="PH_Title"><a class="color-fg-default f4" href="/trending">全网趋势</a></h2></div><div class="Box-sc-g0xbh4-0 bnmsDs" data-component="PH_Actions"><nav class="Breadcrumbs__BreadcrumbsBase-sc-9m4wsf-1 cJTTqz" aria-label="Breadcrumbs"><ol class="Box-sc-g0xbh4-0 eItYAW"><li class="Breadcrumbs__Wrapper-sc-9m4wsf-0 bpSOTI"><a href="/trending" class="Breadcrumbs__BreadcrumbsItem-sc-9m4wsf-2 hztLzL selected" aria-current="page">今天</a></li><li class="Breadcrumbs__Wrapper-sc-9m4wsf-0 bpSOTI"><a href="/trending?since=weekly" class="Breadcrumbs__BreadcrumbsItem-sc-9m4wsf-2 hztLzL">本周</a></li><li class="Breadcrumbs__Wrapper-sc-9m4wsf-0 bpSOTI"><a href="/trending?since=monthly" class="Breadcrumbs__BreadcrumbsItem-sc-9m4wsf-2 hztLzL">本月</a></li></ol></nav></div></div><section class="mt-1 mb-5"><div class="Boxz"><div class="color-bg-subtle Box-rowz rounded-1"><div class="grid grid-cols-12 gap-3"><div class="col-span-12 md:col-span-6"><div class="Box" style="display:flex;overflow:hidden;height:100%;cursor:pointer"><div class="repo-card anim-hover-grow"><div style="flex-grow:1"><div class="py-2 px-3 pb-2"><div class="mb-3 flex items-center"><img data-component="Avatar" class="Avatar__StyledAvatar-sc-2lv0r8-0 kRIKrw mr-3" size="35" src="https://static.github-zh.com/github_avatars/Genesis-Embodied-AI?size=35" height="35" width="35"/><div class="d-flex flex-column"><a style="display:block;width:100%;font-size:18px" rel="nofollow noopener" href="https://github.com/Genesis-Embodied-AI/Genesis" class="text-bold color-fg-default">Genesis</a><div class="flex items-center"><a style="display:flex" rel="nofollow noopener" href="https://github.com/Genesis-Embodied-AI" class="color-fg-muted"><span style="display:block" class="flex-none truncate">@<!-- -->Genesis-Embodied-AI</span></a></div></div></div><div class="body"><p class="color-fg-muted mb-0"><span>Genesis &#26159;&#19987;&#20026;&#26426;&#22120;&#20154;/&#23884;&#20837;&#24335; AI/&#29289;&#29702; AI &#24212;&#29992;&#35774;&#35745;&#30340;&#36890;&#29992;&#29289;&#29702;&#24179;&#21488;</span></p></div><div class="d-flex flex-wrap pt-2"></div></div></div><div class="py-2 px-3"><div style="display:flex;justify-content:space-between;width:100%"><div><span class="text-small mr-2 color-fg-subtle"><span style="background:#3572A5" class="point"></span> <!-- -->Python</span><span class="text-small mr-1 color-fg-subtle"><svg aria-hidden="true" focusable="false" class="octicon octicon-star" viewBox="0 0 16 16" width="14" height="14" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M8 .25a.75.75 0 0 1 .673.418l1.882 3.815 4.21.612a.75.75 0 0 1 .416 1.279l-3.046 2.97.719 4.192a.751.751 0 0 1-1.088.791L8 12.347l-3.766 1.98a.75.75 0 0 1-1.088-.79l.72-4.194L.818 6.374a.75.75 0 0 1 .416-1.28l4.21-.611L7.327.668A.75.75 0 0 1 8 .25Zm0 2.445L6.615 5.5a.75.75 0 0 1-.564.41l-3.097.45 2.24 2.184a.75.75 0 0 1 .216.664l-.528 3.084 2.769-1.456a.75.75 0 0 1 .698 0l2.77 1.456-.53-3.084a.75.75 0 0 1 .216-.664l2.24-2.183-3.096-.45a.75.75 0 0 1-.564-.41L8 2.694Z"></path></svg>26.44 k</span><span class="mx-2 color-fg-muted summary-icon"><svg height="16" stroke-linejoin="round" viewBox="0 0 16 16" width="16" style="color:currentcolor"><path d="M2.5 0.5V0H3.5V0.5C3.5 1.60457 4.39543 2.5 5.5 2.5H6V3V3.5H5.5C4.39543 3.5 3.5 4.39543 3.5 5.5V6H3H2.5V5.5C2.5 4.39543 1.60457 3.5 0.5 3.5H0V3V2.5H0.5C1.60457 2.5 2.5 1.60457 2.5 0.5Z" fill="currentColor"></path><path d="M14.5 4.5V5H13.5V4.5C13.5 3.94772 13.0523 3.5 12.5 3.5H12V3V2.5H12.5C13.0523 2.5 13.5 2.05228 13.5 1.5V1H14H14.5V1.5C14.5 2.05228 14.9477 2.5 15.5 2.5H16V3V3.5H15.5C14.9477 3.5 14.5 3.94772 14.5 4.5Z" fill="currentColor"></path><path d="M8.40706 4.92939L8.5 4H9.5L9.59294 4.92939C9.82973 7.29734 11.7027 9.17027 14.0706 9.40706L15 9.5V10.5L14.0706 10.5929C11.7027 10.8297 9.82973 12.7027 9.59294 15.0706L9.5 16H8.5L8.40706 15.0706C8.17027 12.7027 6.29734 10.8297 3.92939 10.5929L3 10.5V9.5L3.92939 9.40706C6.29734 9.17027 8.17027 7.29734 8.40706 4.92939Z" fill="currentColor"></path></svg></span></div><div class="text-small color-fg-subtle"><span>1 天前</span></div></div></div></div></div></div><div class="col-span-12 md:col-span-6"><div class="Box" style="display:flex;overflow:hidden;height:100%;cursor:pointer"><div class="repo-card anim-hover-grow"><div style="flex-grow:1"><div class="py-2 px-3 pb-2"><div class="mb-3 flex items-center"><img data-component="Avatar" class="Avatar__StyledAvatar-sc-2lv0r8-0 kRIKrw mr-3" size="35" src="https://static.github-zh.com/github_avatars/Shubhamsaboo?size=35" height="35" width="35"/><div class="d-flex flex-column"><a style="display:block;width:100%;font-size:18px" rel="nofollow noopener" href="https://github.com/Shubhamsaboo/awesome-llm-apps" class="text-bold color-fg-default">awesome-llm-apps</a><div class="flex items-center"><a style="display:flex" rel="nofollow noopener" href="https://github.com/Shubhamsaboo" class="color-fg-muted"><span style="display:block" class="flex-none truncate">@<!-- -->Shubhamsaboo</span></a></div></div></div><div class="body"><p class="color-fg-muted mb-0"><span>&#31934;&#36873;&#25910;&#24405;&#37319;&#29992;RAG&#12289;AI&#26234;&#33021;&#20307;&#12289;MCP&#12289;&#35821;&#38899;&#26234;&#33021;&#20307;&#31561;&#25216;&#26415;&#26500;&#24314;&#30340;&#20248;&#36136;LLM&#24212;&#29992;&#12290;</span></p></div><div class="d-flex flex-wrap pt-2"><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/llms">大语言模型</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/rag">rag</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/python">Python</a></div></div></div><div class="py-2 px-3"><div style="display:flex;justify-content:space-between;width:100%"><div><span class="text-small mr-2 color-fg-subtle"><span style="background:#3572A5" class="point"></span> <!-- -->Python</span><span class="text-small mr-1 color-fg-subtle"><svg aria-hidden="true" focusable="false" class="octicon octicon-star" viewBox="0 0 16 16" width="14" height="14" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M8 .25a.75.75 0 0 1 .673.418l1.882 3.815 4.21.612a.75.75 0 0 1 .416 1.279l-3.046 2.97.719 4.192a.751.751 0 0 1-1.088.791L8 12.347l-3.766 1.98a.75.75 0 0 1-1.088-.79l.72-4.194L.818 6.374a.75.75 0 0 1 .416-1.28l4.21-.611L7.327.668A.75.75 0 0 1 8 .25Zm0 2.445L6.615 5.5a.75.75 0 0 1-.564.41l-3.097.45 2.24 2.184a.75.75 0 0 1 .216.664l-.528 3.084 2.769-1.456a.75.75 0 0 1 .698 0l2.77 1.456-.53-3.084a.75.75 0 0 1 .216-.664l2.24-2.183-3.096-.45a.75.75 0 0 1-.564-.41L8 2.694Z"></path></svg>52.51 k</span><span class="mx-2 color-fg-muted summary-icon"><svg height="16" stroke-linejoin="round" viewBox="0 0 16 16" width="16" style="color:currentcolor"><path d="M2.5 0.5V0H3.5V0.5C3.5 1.60457 4.39543 2.5 5.5 2.5H6V3V3.5H5.5C4.39543 3.5 3.5 4.39543 3.5 5.5V6H3H2.5V5.5C2.5 4.39543 1.60457 3.5 0.5 3.5H0V3V2.5H0.5C1.60457 2.5 2.5 1.60457 2.5 0.5Z" fill="currentColor"></path><path d="M14.5 4.5V5H13.5V4.5C13.5 3.94772 13.0523 3.5 12.5 3.5H12V3V2.5H12.5C13.0523 2.5 13.5 2.05228 13.5 1.5V1H14H14.5V1.5C14.5 2.05228 14.9477 2.5 15.5 2.5H16V3V3.5H15.5C14.9477 3.5 14.5 3.94772 14.5 4.5Z" fill="currentColor"></path><path d="M8.40706 4.92939L8.5 4H9.5L9.59294 4.92939C9.82973 7.29734 11.7027 9.17027 14.0706 9.40706L15 9.5V10.5L14.0706 10.5929C11.7027 10.8297 9.82973 12.7027 9.59294 15.0706L9.5 16H8.5L8.40706 15.0706C8.17027 12.7027 6.29734 10.8297 3.92939 10.5929L3 10.5V9.5L3.92939 9.40706C6.29734 9.17027 8.17027 7.29734 8.40706 4.92939Z" fill="currentColor"></path></svg></span></div><div class="text-small color-fg-subtle"><span>2 天前</span></div></div></div></div></div></div><div class="col-span-12 md:col-span-6"><div class="Box" style="display:flex;overflow:hidden;height:100%;cursor:pointer"><div class="repo-card anim-hover-grow"><div style="flex-grow:1"><div style="position:relative;height:0"><img alt="frappe/hrms" loading="lazy" decoding="async" data-nimg="fill" class="rounded-top-1" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;color:transparent" src="https://repository-images.github-zh.cn/501292795/010f358b-8afb-4e41-a7c8-8444983055c8"/></div><div class="py-2 px-3 pb-2"><div class="mb-3 flex items-center"><img data-component="Avatar" class="Avatar__StyledAvatar-sc-2lv0r8-0 kRIKrw mr-3" alt="Frappe" size="35" src="https://static.github-zh.com/github_avatars/frappe?size=35" height="35" width="35"/><div class="d-flex flex-column"><a style="display:block;width:100%;font-size:18px" rel="nofollow noopener" href="https://github.com/frappe/hrms" class="text-bold color-fg-default">hrms</a><div class="flex items-center"><a style="display:flex" rel="nofollow noopener" href="https://github.com/frappe" class="color-fg-muted"><span style="display:block" class="truncate shrink">Frappe</span><span style="display:block" class="flex-none truncate">@<!-- -->frappe</span></a></div></div></div><div class="body"><p class="color-fg-muted mb-0"><span>Frappe HR &#26159;&#19968;&#27454;&#24320;&#28304;&#12289;&#29616;&#20195;&#21270;&#30340;&#20154;&#21147;&#36164;&#28304;&#19982;&#34218;&#36164;&#31649;&#29702;&#36719;&#20214;</span></p></div><div class="d-flex flex-wrap pt-2"><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/hr">hr</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/hrms">hrms</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/payroll">payroll</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/erpnext">erpnext</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/hcm">hcm</a></div></div></div><div class="py-2 px-3"><div style="display:flex;justify-content:space-between;width:100%"><div><span class="text-small mr-2 color-fg-subtle"><span style="background:#3572A5" class="point"></span> <!-- -->Python</span><span class="text-small mr-1 color-fg-subtle"><svg aria-hidden="true" focusable="false" class="octicon octicon-star" viewBox="0 0 16 16" width="14" height="14" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M8 .25a.75.75 0 0 1 .673.418l1.882 3.815 4.21.612a.75.75 0 0 1 .416 1.279l-3.046 2.97.719 4.192a.751.751 0 0 1-1.088.791L8 12.347l-3.766 1.98a.75.75 0 0 1-1.088-.79l.72-4.194L.818 6.374a.75.75 0 0 1 .416-1.28l4.21-.611L7.327.668A.75.75 0 0 1 8 .25Zm0 2.445L6.615 5.5a.75.75 0 0 1-.564.41l-3.097.45 2.24 2.184a.75.75 0 0 1 .216.664l-.528 3.084 2.769-1.456a.75.75 0 0 1 .698 0l2.77 1.456-.53-3.084a.75.75 0 0 1 .216-.664l2.24-2.183-3.096-.45a.75.75 0 0 1-.564-.41L8 2.694Z"></path></svg>4.98 k</span><span class="mx-2 color-fg-muted summary-icon"><svg height="16" stroke-linejoin="round" viewBox="0 0 16 16" width="16" style="color:currentcolor"><path d="M2.5 0.5V0H3.5V0.5C3.5 1.60457 4.39543 2.5 5.5 2.5H6V3V3.5H5.5C4.39543 3.5 3.5 4.39543 3.5 5.5V6H3H2.5V5.5C2.5 4.39543 1.60457 3.5 0.5 3.5H0V3V2.5H0.5C1.60457 2.5 2.5 1.60457 2.5 0.5Z" fill="currentColor"></path><path d="M14.5 4.5V5H13.5V4.5C13.5 3.94772 13.0523 3.5 12.5 3.5H12V3V2.5H12.5C13.0523 2.5 13.5 2.05228 13.5 1.5V1H14H14.5V1.5C14.5 2.05228 14.9477 2.5 15.5 2.5H16V3V3.5H15.5C14.9477 3.5 14.5 3.94772 14.5 4.5Z" fill="currentColor"></path><path d="M8.40706 4.92939L8.5 4H9.5L9.59294 4.92939C9.82973 7.29734 11.7027 9.17027 14.0706 9.40706L15 9.5V10.5L14.0706 10.5929C11.7027 10.8297 9.82973 12.7027 9.59294 15.0706L9.5 16H8.5L8.40706 15.0706C8.17027 12.7027 6.29734 10.8297 3.92939 10.5929L3 10.5V9.5L3.92939 9.40706C6.29734 9.17027 8.17027 7.29734 8.40706 4.92939Z" fill="currentColor"></path></svg></span></div><div class="text-small color-fg-subtle"><span>2 小时前</span></div></div></div></div></div></div><div class="col-span-12 md:col-span-6"><div class="Box" style="display:flex;overflow:hidden;height:100%;cursor:pointer"><div class="repo-card anim-hover-grow"><div style="flex-grow:1"><div style="position:relative;height:0"><img alt="juspay/hyperswitch" loading="lazy" decoding="async" data-nimg="fill" class="rounded-top-1" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;color:transparent" src="https://repository-images.github-zh.cn/552877440/fd8b83bc-a093-4f7b-9e16-a0cd1f9f8572"/></div><div class="py-2 px-3 pb-2"><div class="mb-3 flex items-center"><img data-component="Avatar" class="Avatar__StyledAvatar-sc-2lv0r8-0 kRIKrw mr-3" size="35" src="https://static.github-zh.com/github_avatars/juspay?size=35" height="35" width="35"/><div class="d-flex flex-column"><a style="display:block;width:100%;font-size:18px" rel="nofollow noopener" href="https://github.com/juspay/hyperswitch" class="text-bold color-fg-default">hyperswitch</a><div class="flex items-center"><a style="display:flex" rel="nofollow noopener" href="https://github.com/juspay" class="color-fg-muted"><span style="display:block" class="flex-none truncate">@<!-- -->juspay</span></a></div></div></div><div class="body"><p class="color-fg-muted mb-0"><span>hyperswitch &#26159;&#19968;&#31181;&#29992;Rust&#32534;&#20889;&#30340;&#25903;&#20184;&#20999;&#25442;&#20195;&#29702;，&#21487;&#35753;&#24744;&#36890;&#36807;&#21333;&#20010;API &#38598;&#25104;&#36830;&#25509;&#22810;&#20010;&#25903;&#20184;&#22788;&#29702;&#31995;&#32479;</span></p></div><div class="d-flex flex-wrap pt-2"><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/payments">payments</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/rust">Rust</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/orchestration">orchestration</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/hacktoberfest">Hacktoberfest</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/beginner-friendly">beginner-friendly</a></div></div></div><div class="py-2 px-3"><div style="display:flex;justify-content:space-between;width:100%"><div><span class="text-small mr-2 color-fg-subtle"><span style="background:#dea584" class="point"></span> <!-- -->Rust</span><span class="text-small mr-1 color-fg-subtle"><svg aria-hidden="true" focusable="false" class="octicon octicon-star" viewBox="0 0 16 16" width="14" height="14" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M8 .25a.75.75 0 0 1 .673.418l1.882 3.815 4.21.612a.75.75 0 0 1 .416 1.279l-3.046 2.97.719 4.192a.751.751 0 0 1-1.088.791L8 12.347l-3.766 1.98a.75.75 0 0 1-1.088-.79l.72-4.194L.818 6.374a.75.75 0 0 1 .416-1.28l4.21-.611L7.327.668A.75.75 0 0 1 8 .25Zm0 2.445L6.615 5.5a.75.75 0 0 1-.564.41l-3.097.45 2.24 2.184a.75.75 0 0 1 .216.664l-.528 3.084 2.769-1.456a.75.75 0 0 1 .698 0l2.77 1.456-.53-3.084a.75.75 0 0 1 .216-.664l2.24-2.183-3.096-.45a.75.75 0 0 1-.564-.41L8 2.694Z"></path></svg>23.05 k</span><span class="mx-2 color-fg-muted summary-icon"><svg height="16" stroke-linejoin="round" viewBox="0 0 16 16" width="16" style="color:currentcolor"><path d="M2.5 0.5V0H3.5V0.5C3.5 1.60457 4.39543 2.5 5.5 2.5H6V3V3.5H5.5C4.39543 3.5 3.5 4.39543 3.5 5.5V6H3H2.5V5.5C2.5 4.39543 1.60457 3.5 0.5 3.5H0V3V2.5H0.5C1.60457 2.5 2.5 1.60457 2.5 0.5Z" fill="currentColor"></path><path d="M14.5 4.5V5H13.5V4.5C13.5 3.94772 13.0523 3.5 12.5 3.5H12V3V2.5H12.5C13.0523 2.5 13.5 2.05228 13.5 1.5V1H14H14.5V1.5C14.5 2.05228 14.9477 2.5 15.5 2.5H16V3V3.5H15.5C14.9477 3.5 14.5 3.94772 14.5 4.5Z" fill="currentColor"></path><path d="M8.40706 4.92939L8.5 4H9.5L9.59294 4.92939C9.82973 7.29734 11.7027 9.17027 14.0706 9.40706L15 9.5V10.5L14.0706 10.5929C11.7027 10.8297 9.82973 12.7027 9.59294 15.0706L9.5 16H8.5L8.40706 15.0706C8.17027 12.7027 6.29734 10.8297 3.92939 10.5929L3 10.5V9.5L3.92939 9.40706C6.29734 9.17027 8.17027 7.29734 8.40706 4.92939Z" fill="currentColor"></path></svg></span></div><div class="text-small color-fg-subtle"><span>2 小时前</span></div></div></div></div></div></div><div class="col-span-12 md:col-span-6"><div class="Box" style="display:flex;overflow:hidden;height:100%;cursor:pointer"><div class="repo-card anim-hover-grow"><div style="flex-grow:1"><div class="py-2 px-3 pb-2"><div class="mb-3 flex items-center"><img data-component="Avatar" class="Avatar__StyledAvatar-sc-2lv0r8-0 kRIKrw mr-3" size="35" src="https://static.github-zh.com/github_avatars/daveebbelaar?size=35" height="35" width="35"/><div class="d-flex flex-column"><a style="display:block;width:100%;font-size:18px" rel="nofollow noopener" href="https://github.com/daveebbelaar/ai-cookbook" class="text-bold color-fg-default">ai-cookbook</a><div class="flex items-center"><a style="display:flex" rel="nofollow noopener" href="https://github.com/daveebbelaar" class="color-fg-muted"><span style="display:block" class="flex-none truncate">@<!-- -->daveebbelaar</span></a></div></div></div><div class="body"><p class="color-fg-muted mb-0"><a class="mr-2" style="font-size:95%" href="/collections/llm">#<!-- -->大语言模型<!-- -->#</a><span>Examples and tutorials to help developers build AI systems</span></p></div><div class="d-flex flex-wrap pt-2"><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/agents">agents</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/ai">人工智能</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/anthropic">anthropic</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/llm">大语言模型</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/openai">openai</a></div></div></div><div class="py-2 px-3"><div style="display:flex;justify-content:space-between;width:100%"><div><span class="text-small mr-2 color-fg-subtle"><span style="background:#3572A5" class="point"></span> <!-- -->Python</span><span class="text-small mr-1 color-fg-subtle"><svg aria-hidden="true" focusable="false" class="octicon octicon-star" viewBox="0 0 16 16" width="14" height="14" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M8 .25a.75.75 0 0 1 .673.418l1.882 3.815 4.21.612a.75.75 0 0 1 .416 1.279l-3.046 2.97.719 4.192a.751.751 0 0 1-1.088.791L8 12.347l-3.766 1.98a.75.75 0 0 1-1.088-.79l.72-4.194L.818 6.374a.75.75 0 0 1 .416-1.28l4.21-.611L7.327.668A.75.75 0 0 1 8 .25Zm0 2.445L6.615 5.5a.75.75 0 0 1-.564.41l-3.097.45 2.24 2.184a.75.75 0 0 1 .216.664l-.528 3.084 2.769-1.456a.75.75 0 0 1 .698 0l2.77 1.456-.53-3.084a.75.75 0 0 1 .216-.664l2.24-2.183-3.096-.45a.75.75 0 0 1-.564-.41L8 2.694Z"></path></svg>1.92 k</span><span class="mx-2 color-fg-muted summary-icon"><svg height="16" stroke-linejoin="round" viewBox="0 0 16 16" width="16" style="color:currentcolor"><path d="M2.5 0.5V0H3.5V0.5C3.5 1.60457 4.39543 2.5 5.5 2.5H6V3V3.5H5.5C4.39543 3.5 3.5 4.39543 3.5 5.5V6H3H2.5V5.5C2.5 4.39543 1.60457 3.5 0.5 3.5H0V3V2.5H0.5C1.60457 2.5 2.5 1.60457 2.5 0.5Z" fill="currentColor"></path><path d="M14.5 4.5V5H13.5V4.5C13.5 3.94772 13.0523 3.5 12.5 3.5H12V3V2.5H12.5C13.0523 2.5 13.5 2.05228 13.5 1.5V1H14H14.5V1.5C14.5 2.05228 14.9477 2.5 15.5 2.5H16V3V3.5H15.5C14.9477 3.5 14.5 3.94772 14.5 4.5Z" fill="currentColor"></path><path d="M8.40706 4.92939L8.5 4H9.5L9.59294 4.92939C9.82973 7.29734 11.7027 9.17027 14.0706 9.40706L15 9.5V10.5L14.0706 10.5929C11.7027 10.8297 9.82973 12.7027 9.59294 15.0706L9.5 16H8.5L8.40706 15.0706C8.17027 12.7027 6.29734 10.8297 3.92939 10.5929L3 10.5V9.5L3.92939 9.40706C6.29734 9.17027 8.17027 7.29734 8.40706 4.92939Z" fill="currentColor"></path></svg></span></div><div class="text-small color-fg-subtle"><span>3 天前</span></div></div></div></div></div></div><div class="col-span-12 md:col-span-6"><div class="Box" style="display:flex;overflow:hidden;height:100%;cursor:pointer"><div class="repo-card anim-hover-grow"><div style="flex-grow:1"><div style="position:relative;height:0"><img alt="tldr-pages/tldr" loading="lazy" decoding="async" data-nimg="fill" class="rounded-top-1" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;color:transparent" src="https://repository-images.github-zh.cn/15019962/aa6a8d00-b4a3-11ea-92f4-5cca1da75be2"/></div><div class="py-2 px-3 pb-2"><div class="mb-3 flex items-center"><img data-component="Avatar" class="Avatar__StyledAvatar-sc-2lv0r8-0 kRIKrw mr-3" alt="tldr pages" size="35" src="https://static.github-zh.com/github_avatars/tldr-pages?size=35" height="35" width="35"/><div class="d-flex flex-column"><a style="display:block;width:100%;font-size:18px" rel="nofollow noopener" href="https://github.com/tldr-pages/tldr" class="text-bold color-fg-default">tldr</a><div class="flex items-center"><a style="display:flex" rel="nofollow noopener" href="https://github.com/tldr-pages" class="color-fg-muted"><span style="display:block" class="truncate shrink">tldr pages</span><span style="display:block" class="flex-none truncate">@<!-- -->tldr-pages</span></a></div></div></div><div class="body"><p class="color-fg-muted mb-0"><a class="mr-2" style="font-size:95%" href="/collections/productivity-tools">#<!-- -->效率工具集合<!-- -->#</a><span>tldr &#26159;&#19968;&#20010;&#21629;&#20196;&#29992;&#27861;&#24110;&#21161;&#26597;&#35810;&#24037;&#20855;，&#27604;&#20256;&#32479;&#30340; man &#21629;&#20196;&#26356;&#22909;&#29992;&#12290;</span></p></div><div class="d-flex flex-wrap pt-2"><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/shell">Shell</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/man-page">man-page</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/tldr">tldr pages</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/manpages">manpages</a><a rel="nofollow noopener" style="position:relative" class="card-topic-tag mb-2 f6 mb-2" href="/topics/documentation">文档</a></div></div></div><div class="py-2 px-3"><div style="display:flex;justify-content:space-between;width:100%"><div><span class="text-small mr-2 color-fg-subtle"><span style="background:#083fa1" class="point"></span> <!-- -->Markdown</span><span class="text-small mr-1 color-fg-subtle"><svg aria-hidden="true" focusable="false" class="octicon octicon-star" viewBox="0 0 16 16" width="14" height="14" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M8 .25a.75.75 0 0 1 .673.418l1.882 3.815 4.21.612a.75.75 0 0 1 .416 1.279l-3.046 2.97.719 4.192a.751.751 0 0 1-1.088.791L8 12.347l-3.766 1.98a.75.75 0 0 1-1.088-.79l.72-4.194L.818 6.374a.75.75 0 0 1 .416-1.28l4.21-.611L7.327.668A.75.75 0 0 1 8 .25Zm0 2.445L6.615 5.5a.75.75 0 0 1-.564.41l-3.097.45 2.24 2.184a.75.75 0 0 1 .216.664l-.528 3.084 2.769-1.456a.75.75 0 0 1 .698 0l2.77 1.456-.53-3.084a.75.75 0 0 1 .216-.664l2.24-2.183-3.096-.45a.75.75 0 0 1-.564-.41L8 2.694Z"></path></svg>56.93 k</span><span class="mx-2 color-fg-muted summary-icon"><svg height="16" stroke-linejoin="round" viewBox="0 0 16 16" width="16" style="color:currentcolor"><path d="M2.5 0.5V0H3.5V0.5C3.5 1.60457 4.39543 2.5 5.5 2.5H6V3V3.5H5.5C4.39543 3.5 3.5 4.39543 3.5 5.5V6H3H2.5V5.5C2.5 4.39543 1.60457 3.5 0.5 3.5H0V3V2.5H0.5C1.60457 2.5 2.5 1.60457 2.5 0.5Z" fill="currentColor"></path><path d="M14.5 4.5V5H13.5V4.5C13.5 3.94772 13.0523 3.5 12.5 3.5H12V3V2.5H12.5C13.0523 2.5 13.5 2.05228 13.5 1.5V1H14H14.5V1.5C14.5 2.05228 14.9477 2.5 15.5 2.5H16V3V3.5H15.5C14.9477 3.5 14.5 3.94772 14.5 4.5Z" fill="currentColor"></path><path d="M8.40706 4.92939L8.5 4H9.5L9.59294 4.92939C9.82973 7.29734 11.7027 9.17027 14.0706 9.40706L15 9.5V10.5L14.0706 10.5929C11.7027 10.8297 9.82973 12.7027 9.59294 15.0706L9.5 16H8.5L8.40706 15.0706C8.17027 12.7027 6.29734 10.8297 3.92939 10.5929L3 10.5V9.5L3.92939 9.40706C6.29734 9.17027 8.17027 7.29734 8.40706 4.92939Z" fill="currentColor"></path></svg></span></div><div class="text-small color-fg-subtle"><span>1 小时前</span></div></div></div></div></div></div></div></div></div><div style="text-align:center;margin:10px 20px"><button type="button" class="types__StyledButton-sc-ws60qy-0 ctABYc" data-loading="false" data-no-visuals="true" data-size="small" aria-describedby=":R3pl7pjdb:-loading-announcement"><span data-component="buttonContent" class="Box-sc-g0xbh4-0 gUkoLg"><span data-component="text">展开 <svg aria-hidden="true" focusable="false" class="octicon octicon-arrow-down" viewBox="0 0 16 16" width="15" height="15" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M13.03 8.22a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L3.47 9.28a.751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018l2.97 2.97V3.75a.75.75 0 0 1 1.5 0v7.44l2.97-2.97a.75.75 0 0 1 1.06 0Z"></path></svg></span></span></button><a style="color:#777;font-size:small" href="/trends/weekly"> 查看往期趋势 →</a></div></section></section><noscript></noscript></div></div></div></section><section class="bg-white pb-5"><div class="md:container lg:container mx-auto pt-0 mt-0"><div class="flex flex-row justify-center"><div class="basis-full lg:basis-9/12"><section><h2 class="color-fg-default f4 mb-2 px-2 font-medium">活跃组织</h2><p class="px-2 text-slate-500 text-sm">90%+, 全球 Top100 公司正在使用GitHub</p><div class="d-flex flex-wrap"><div class="col-4 col-sm-2 px-3 mt-3"><a class="text-slate-700" href="/org/microsoft"><img style="width:40px" src="https://static.github-zh.com/github_avatars/microsoft?size=48"/><p>微软</p></a></div><div class="col-4 col-sm-2 px-3 mt-3"><a class="text-slate-700" href="/org/google"><img style="width:40px" src="https://static.github-zh.com/github_avatars/google?size=48"/><p>Google</p></a></div><div class="col-4 col-sm-2 px-3 mt-3"><a class="text-slate-700" href="/org/facebook"><img style="width:40px" src="https://static.github-zh.com/github_avatars/facebook?size=48"/><p>Meta</p></a></div><div class="col-4 col-sm-2 px-3 mt-3"><a class="text-slate-700" href="/org/apache"><img style="width:40px" src="https://static.github-zh.com/github_avatars/apache?size=48"/><p>Apache</p></a></div><div class="col-4 col-sm-2 px-3 mt-3"><a class="text-slate-700" href="/org/alibaba"><img style="width:40px" src="https://static.github-zh.com/github_avatars/alibaba?size=48"/><p>阿里巴巴</p></a></div><div class="col-4 col-sm-2 px-3 mt-3"><a class="text-slate-700" href="/org/Tencent"><img style="width:40px" src="https://static.github-zh.com/github_avatars/Tencent?size=48"/><p>腾讯</p></a></div><div class="col-4 col-sm-2 px-3 mt-3"><a class="text-slate-700" href="/org/didi"><img style="width:40px" src="https://static.github-zh.com/github_avatars/didi?size=48"/><p>滴滴出行</p></a></div><div class="col-4 col-sm-2 px-3 mt-3"><a class="text-slate-700" href="/org/bilibili"><img style="width:40px" src="https://static.github-zh.com/github_avatars/bilibili?size=48"/><p>哔哩哔哩</p></a></div><div class="col-4 col-sm-2 px-3 mt-3"><a class="text-slate-700" href="/org/bytedance"><img style="width:40px" src="https://static.github-zh.com/github_avatars/bytedance?size=48"/><p>字节跳动</p></a></div><div class="col-4 col-sm-2 px-3 mt-3"><a class="text-slate-700" href="/org/adobe"><img style="width:40px" src="https://static.github-zh.com/github_avatars/adobe?size=48"/><p>Adobe</p></a></div><div class="col-4 col-sm-2 px-3 mt-3"><a class="text-slate-700" href="/org/baidu"><img style="width:40px" src="https://static.github-zh.com/github_avatars/baidu?size=48"/><p>百度</p></a></div><div class="col-4 col-sm-2 px-3 mt-3"><a class="text-slate-700" href="/org/Meituan-Dianping"><img style="width:40px" src="https://static.github-zh.com/github_avatars/Meituan-Dianping?size=48"/><p>美团</p></a></div></div></section></div></div></div></section><section class="py-5" style="background:#f6f8fa"><div class="md:container lg:container mx-auto pt-0 mt-0"><div class="flex flex-row justify-center"><div class="basis-full lg:basis-9/12"><section class="mt-4 mb-5"><h2 class="color-fg-default f4 mb-2 px-2 font-medium"><span style="color:rgb(220, 0, 78)"><svg aria-hidden="true" focusable="false" class="octicon octicon-heart" viewBox="0 0 16 16" width="16" height="16" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="m8 14.25.345.666a.75.75 0 0 1-.69 0l-.008-.004-.018-.01a7.152 7.152 0 0 1-.31-.17 22.055 22.055 0 0 1-3.434-2.414C2.045 10.731 0 8.35 0 5.5 0 2.836 2.086 1 4.25 1 5.797 1 7.153 1.802 8 3.02 8.847 1.802 10.203 1 11.75 1 13.914 1 16 2.836 16 5.5c0 2.85-2.045 5.231-3.885 6.818a22.066 22.066 0 0 1-3.744 2.584l-.018.01-.006.003h-.002ZM4.25 2.5c-1.336 0-2.75 1.164-2.75 3 0 2.15 1.58 4.144 3.365 5.682A20.58 20.58 0 0 0 8 13.393a20.58 20.58 0 0 0 3.135-2.211C12.92 9.644 14.5 7.65 14.5 5.5c0-1.836-1.414-3-2.75-3-1.373 0-2.609.986-3.029 2.456a.749.749 0 0 1-1.442 0C6.859 3.486 5.623 2.5 4.25 2.5Z"></path></svg></span> 最受欢迎的页面</h2><div class="d-flex flex-wrap"><div class="col-6 col-sm-4 p-2"><a href="/collections/learn-to-code">面试与学习资源</a></div><div class="col-6 col-sm-4 p-2"><a href="/collections/for-beginner">新手必看，学习路线图</a></div><div class="col-6 col-sm-4 p-2"><a href="/top-china">中文项目排行榜</a></div><div class="col-6 col-sm-4 p-2"><a href="/real-world">实战项目</a></div><div class="col-6 col-sm-4 p-2"><a href="/collections/awesome">Awesome系列集合</a></div><div class="col-6 col-sm-4 p-2"><a href="/trends/monthly">GitHub趋势月报</a></div></div></section><h2 class="color-fg-default f4 mb-2 px-2 font-medium"><svg aria-hidden="true" focusable="false" class="mr-1" viewBox="0 0 16 16" width="16" height="16" fill="currentColor" style="display:inline-block;user-select:none;vertical-align:text-bottom;overflow:visible"><path d="M6.368 1.01a.75.75 0 0 1 .623.859L6.57 4.5h3.98l.46-2.868a.75.75 0 0 1 1.48.237L12.07 4.5h2.18a.75.75 0 0 1 0 1.5h-2.42l-.64 4h2.56a.75.75 0 0 1 0 1.5h-2.8l-.46 2.869a.75.75 0 0 1-1.48-.237l.42-2.632H5.45l-.46 2.869a.75.75 0 0 1-1.48-.237l.42-2.632H1.75a.75.75 0 0 1 0-1.5h2.42l.64-4H2.25a.75.75 0 0 1 0-1.5h2.8l.46-2.868a.75.75 0 0 1 .858-.622ZM9.67 10l.64-4H6.33l-.64 4Z"></path></svg><a href="/topics">按主题浏览</a></h2></div></div></div></section><section class="bg-white p-1 py-5"><div class="container-lg" style="text-align:center"><h2 class="text-lg mb-2 text-normal">GitHub 插件推荐</h2><p class="mb-2 color-fg-subtle">工欲善其事必先利其器，善于利用插件能让您事半功倍。</p><div class="d-flex flex-wrap"><div class="col-6 col-sm-4"><div style="padding:15px"><h4 class="h4 mb-1"><a class="color-fg-accent" href="https://github.com/sindresorhus/refined-github">Refined GitHub</a></h4><p class="color-fg-subtle">优化GitHub功能，使其变得更好用。提供了诸如“支持下载某个目录”等实用小功能。</p></div></div><div class="col-6 col-sm-4"><div style="padding:15px"><h4 class="h4 mb-1"><a class="color-fg-accent" href="https://github.com/k1995/github-i18n-plugin">GitHub汉化插件</a></h4><p class="color-fg-subtle">汉化GitHub菜单栏，并支持将英文项目简介翻译为中文。</p></div></div><div class="col-12 col-sm-4"><div style="padding:15px"><h4 class="h4 mb-1"><a class="color-fg-accent" href="https://greasyfork.org/zh-CN/scripts/412245-github-%E5%A2%9E%E5%BC%BA-%E9%AB%98%E9%80%9F%E4%B8%8B%E8%BD%BD">GitHub高速下载插件</a></h4><p class="color-fg-subtle">是否感到在GitHub上下载文件巨慢？该插件提供高速下载代理服务。</p></div></div></div><br/><p class="color-fg-subtle">寻找更多插件，请戳<a class="color-fg-accent" href="/post/chrome-extensions-for-developer">这里</a></p></div></section></div></div><!--/$--></div><!--$--><!--/$--></div><script type="application/json" id="__PRIMER_DATA_:Rdb:__">{"resolvedServerColorMode":"day"}</script><footer class="pt-6 pb-3 text-small" style="color:#666;background:#f6f8fa"><div class="md:container lg:container mx-auto pt-0 mt-0"><div class="flex flex-row justify-center"><div class="basis-full lg:basis-9/12"><div class="px-3 flex justify-between flex-wrap-reverse"><p class="flex flex-wrap"><span class="mr-3">©2025 GitHub中文社区</span><a class="mr-3" href="https://forum.github-zh.com/">论坛</a><a class="mr-3" href="https://github.com/">GitHub官网</a><a class="mr-3" href="/sitemap">网站地图</a><a style="display:none" href="https://www.github-zh.com/zh-index">GitHub官方翻译</a></p><div><ul class="list-style-none d-flex flex-items-center mb-3 mb-sm-0 lh-condensed-ultra"><li class="mr-3"><a href="https://x.com/github" class="color-fg-subtle"><img src="/static/icon/x.svg" height="16" width="16" class="d-block" loading="lazy" decoding="async" alt="X icon"/><span class="sr-only">GitHub on X</span></a></li><li class="mr-3"><a href="https://www.facebook.com/GitHub" class="color-fg-subtle"><img src="/static/icon/facebook.svg" width="18" height="18" class="d-block" loading="lazy" decoding="async" alt="Facebook icon"/><span class="sr-only">GitHub on Facebook</span></a></li><li class="mr-3 flex-self-start"><a href="https://www.linkedin.com/company/github" class="color-fg-subtle"><img src="/static/icon/linkedin.svg" width="19" height="18" class="d-block" loading="lazy" decoding="async" alt="Linkedin icon"/><span class="sr-only">GitHub on LinkedIn</span></a></li><li class="mr-3"><a href="https://www.youtube.com/github" class="color-fg-subtle"><img src="/static/icon/youtube.svg" width="23" height="16" class="d-block" loading="lazy" decoding="async" alt="YouTube icon"/><span class="sr-only">GitHub on YouTube</span></a></li><li class="mr-3"><a href="https://www.twitch.tv/github" class="color-fg-subtle"><img src="/static/icon/twitch.svg" width="18" height="18" class="d-block" loading="lazy" decoding="async" alt="Twitch icon"/><span class="sr-only">GitHub on Twitch</span></a></li><li class="mr-3"><a href="https://www.tiktok.com/@github" class="color-fg-subtle"><img src="/static/icon/tiktok.svg" width="18" height="18" class="d-block" loading="lazy" decoding="async" alt="TikTok icon"/><span class="sr-only">GitHub on TikTok</span></a></li><li><a href="https://github.com/github" class="color-fg-subtle"><img src="/static/icon/github.svg" width="20" height="20" class="d-block" loading="lazy" decoding="async" alt="GitHub mark"/><span class="sr-only">GitHub’s organization on GitHub</span></a></li></ul></div></div></div></div></div></footer><script src="/_next/static/chunks/webpack-c5bd16fb9e28489f.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:I[52328,[],\"HTTPAccessFallbackBoundary\"]\n2:I[53704,[\"4566\",\"static/chunks/3deddded-68201c8d81665792.js\",\"504\",\"static/chunks/baf71385-d0c999ba092b5fe2.js\",\"8173\",\"static/chunks/8173-d098b821394bd879.js\",\"5805\",\"static/chunks/5805-1abbbe5bb126872b.js\",\"482\",\"static/chunks/482-9be9609530651154.js\",\"5445\",\"static/chunks/5445-da8093459f30f6a0.js\",\"9435\",\"static/chunks/9435-088317c1b3cdacf0.js\",\"7461\",\"static/chunks/7461-439a77a375650687.js\",\"9699\",\"static/chunks/9699-ad33edfb261796d9.js\",\"866\",\"static/chunks/866-3757d5cd14c89021.js\",\"1641\",\"static/chunks/1641-f6301c7124da702e.js\",\"7177\",\"static/chunks/app/layout-861711e7ab4f45f5.js\"],\"\"]\n3:I[22004,[\"4566\",\"static/chunks/3deddded-68201c8d81665792.js\",\"504\",\"static/chunks/baf71385-d0c999ba092b5fe2.js\",\"8173\",\"static/chunks/8173-d098b821394bd879.js\",\"5805\",\"static/chunks/5805-1abbbe5bb126872b.js\",\"482\",\"static/chunks/482-9be9609530651154.js\",\"5445\",\"static/chunks/5445-da8093459f30f6a0.js\",\"9435\",\"static/chunks/9435-088317c1b3cdacf0.js\",\"7461\",\"static/chunks/7461-439a77a375650687.js\",\"9699\",\"static/chunks/9699-ad33edfb261796d9.js\",\"866\",\"static/chunks/866-3757d5cd14c89021.js\",\"1641\",\"static/chunks/1641-f6301c7124da702e.js\",\"7177\",\"static/chunks/app/layout-861711e7ab4f45f5.js\"],\"Providers\"]\n4:\"$Sreact.suspense\"\n5:I[35845,[\"4566\",\"static/chunks/3deddded-68201c8d81665792.js\",\"504\",\"static/chunks/baf71385-d0c999ba092b5fe2.js\",\"8173\",\"static/chunks/8173-d098b821394bd879.js\",\"5805\",\"static/chunks/5805-1abbbe5bb126872b.js\",\"482\",\"static/chunks/482-9be9609530651154.js\",\"5445\",\"static/chunks/5445-da8093459f30f6a0.js\",\"9435\",\"static/chunks/9435-088317c1b3cdacf0.js\",\"7461\",\"static/chunks/7461-439a77a375650687.js\",\"9699\",\"static/chunks/9699-ad33edfb261796d9.js\",\"866\",\"static/chunks/866-3757d5cd14c89021.js\",\"1641\",\"static/chunks/1641-f6301c7124da702e.js\",\"7177\",\"static/chunks/app/layout-861711e7ab4f45f5.js\"],\"default\"]\n6:I[48173,[\"4566\",\"static/chunks/3deddded-68201c8d81665792.js\",\"504\",\"static/chunks/baf71385-d0c999ba092b5fe2.js\",\"8173\",\"static/chunks/8173-d098b821394bd879.js\",\"58"])</script><script>self.__next_f.push([1,"05\",\"static/chunks/5805-1abbbe5bb126872b.js\",\"482\",\"static/chunks/482-9be9609530651154.js\",\"5445\",\"static/chunks/5445-da8093459f30f6a0.js\",\"5565\",\"static/chunks/5565-c0772e77e216bce8.js\",\"9435\",\"static/chunks/9435-088317c1b3cdacf0.js\",\"4584\",\"static/chunks/4584-6111dc905d81e591.js\",\"7461\",\"static/chunks/7461-439a77a375650687.js\",\"3562\",\"static/chunks/3562-eee04cad71fcedad.js\",\"5377\",\"static/chunks/5377-84470fbb5a85c869.js\",\"5632\",\"static/chunks/5632-8a88983323da71bf.js\",\"3033\",\"static/chunks/3033-8eea8d07aec5e7d5.js\",\"6054\",\"static/chunks/6054-607a988849a9c9b2.js\",\"3446\",\"static/chunks/3446-8e433dfa2fac8253.js\",\"8974\",\"static/chunks/app/page-18e0f5b7f45a1756.js\"],\"\"]\n7:I[55327,[\"4566\",\"static/chunks/3deddded-68201c8d81665792.js\",\"504\",\"static/chunks/baf71385-d0c999ba092b5fe2.js\",\"8173\",\"static/chunks/8173-d098b821394bd879.js\",\"5805\",\"static/chunks/5805-1abbbe5bb126872b.js\",\"482\",\"static/chunks/482-9be9609530651154.js\",\"5445\",\"static/chunks/5445-da8093459f30f6a0.js\",\"9435\",\"static/chunks/9435-088317c1b3cdacf0.js\",\"7461\",\"static/chunks/7461-439a77a375650687.js\",\"9699\",\"static/chunks/9699-ad33edfb261796d9.js\",\"866\",\"static/chunks/866-3757d5cd14c89021.js\",\"1641\",\"static/chunks/1641-f6301c7124da702e.js\",\"7177\",\"static/chunks/app/layout-861711e7ab4f45f5.js\"],\"default\"]\n8:I[51358,[\"4566\",\"static/chunks/3deddded-68201c8d81665792.js\",\"504\",\"static/chunks/baf71385-d0c999ba092b5fe2.js\",\"8173\",\"static/chunks/8173-d098b821394bd879.js\",\"5805\",\"static/chunks/5805-1abbbe5bb126872b.js\",\"482\",\"static/chunks/482-9be9609530651154.js\",\"5445\",\"static/chunks/5445-da8093459f30f6a0.js\",\"9435\",\"static/chunks/9435-088317c1b3cdacf0.js\",\"7461\",\"static/chunks/7461-439a77a375650687.js\",\"9699\",\"static/chunks/9699-ad33edfb261796d9.js\",\"866\",\"static/chunks/866-3757d5cd14c89021.js\",\"1641\",\"static/chunks/1641-f6301c7124da702e.js\",\"7177\",\"static/chunks/app/layout-861711e7ab4f45f5.js\"],\"default\"]\n9:I[60766,[\"4566\",\"static/chunks/3deddded-68201c8d81665792.js\",\"504\",\"static/chunks/baf71385-d0c999ba092b5fe2.js\",\"8173\",\"static/chunks/8173-d098b821394bd87"])</script><script>self.__next_f.push([1,"9.js\",\"5805\",\"static/chunks/5805-1abbbe5bb126872b.js\",\"482\",\"static/chunks/482-9be9609530651154.js\",\"5445\",\"static/chunks/5445-da8093459f30f6a0.js\",\"9435\",\"static/chunks/9435-088317c1b3cdacf0.js\",\"7461\",\"static/chunks/7461-439a77a375650687.js\",\"9699\",\"static/chunks/9699-ad33edfb261796d9.js\",\"866\",\"static/chunks/866-3757d5cd14c89021.js\",\"1641\",\"static/chunks/1641-f6301c7124da702e.js\",\"7177\",\"static/chunks/app/layout-861711e7ab4f45f5.js\"],\"GoogleAnalytics\"]\na:I[15244,[],\"\"]\nb:I[43866,[],\"\"]\nc:\"$Sreact.fragment\"\ne:I[86213,[],\"OutletBoundary\"]\n11:I[51188,[\"4209\",\"static/chunks/app/loading-0e11eedaa8ea8b58.js\"],\"default\"]\n12:I[86213,[],\"MetadataBoundary\"]\n14:I[86213,[],\"ViewportBoundary\"]\n16:I[34835,[],\"\"]\n:HL[\"/_next/static/css/a8a3af5f429da1cc.css\",\"style\"]\n:HL[\"/_next/static/css/275e79cfb732fbdf.css\",\"style\"]\n:HL[\"/_next/static/css/864df8640d6e275a.css\",\"style\"]\n:HL[\"/_next/static/css/3caf7682e0d0c7f6.css\",\"style\"]\n:HL[\"/_next/static/css/7369cfca97536b38.css\",\"style\"]\n:HL[\"/_next/static/css/15a7f50c65b3acc7.css\",\"style\"]\n:HL[\"/_next/static/css/fd637fbbdf907f32.css\",\"style\"]\n:HL[\"/_next/static/css/9d85d1493ae1b434.css\",\"style\"]\n:HL[\"/_next/static/css/1aa66934733428bb.css\",\"style\"]\n:HL[\"/_next/static/css/a4515bee888d92d1.css\",\"style\"]\n:HL[\"/_next/static/css/19067771cc4d3ee8.css\",\"style\"]\n:HL[\"/_next/static/css/13fe034d1d788ff0.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"Yh1P9O7NGrIxCaSTi4cof\",\"p\":\"\",\"c\":[\"\",\"?_rsc=5ul08\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}],\"modal\":[\"__DEFAULT__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$L1\",\"c\",{\"notFound\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/a8a3af5f429da1cc.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/275e79cfb732fbdf.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/864df8640d6e275a.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"3\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/3caf7682e0d0c7f6.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"4\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/7369cfca97536b38.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"5\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/15a7f50c65b3acc7.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"6\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/fd637fbbdf907f32.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"7\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/9d85d1493ae1b434.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"8\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/1aa66934733428bb.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"9\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/a4515bee888d92d1.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"suppressHydrationWarning\":true,\"lang\":\"zh-CN\",\"children\":[[\"$\",\"head\",null,{\"children\":[\"$\",\"$L2\",null,{\"data-ad-client\":\"ca-pub-9990268883331928\",\"async\":true,\"src\":\"https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js\"}]}],[\"$\",\"body\",null,{\"children\":[[\"$\",\"$L3\",null,{\"children\":[[\"$\",\"div\",null,{\"children\":[[\"$\",\"div\",null,{\"className\":\"Header d-flex flex-justify-between flex-md-justify-start\",\"style\":{\"zIndex\":1,\"position\":\"relative\"},\"children\":[[\"$\",\"div\",null,{\"className\":\"mr-3\",\"children\":[\"$\",\"$4\",null,{\"fallback\":null,\"children\":[\"$\",\"$L5\",null,{}]}]}],[\"$\",\"div\",null,{\"className\":\"Header-item mr-3\",\"children\":[\"$\",\"$L6\",null,{\"href\":\"/\",\"className\":\"Header-link f4 d-flex flex-items-center\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"aria-hidden\":\"true\",\"tabIndex\":\"$undefined\",\"focusable\":\"false\",\"aria-label\":\"$undefined\",\"aria-labelledby\":\"$undefined\",\"className\":\"octicon octicon-mark-github\",\"role\":\"$undefined\",\"viewBox\":\"0 0 24 24\",\"width\":32,\"height\":32,\"fill\":\"currentColor\",\"id\":\"$undefined\",\"style\":{\"display\":\"inline-block\",\"userSelect\":\"none\",\"verticalAlign\":\"text-bottom\",\"overflow\":\"visible\"},\"children\":[null,[\"$\",\"path\",null,{\"d\":\"M12.5.75C6.146.75 1 5.896 1 12.25c0 5.089 3.292 9.387 7.863 10.91.575.101.79-.244.79-.546 0-.273-.014-1.178-.014-2.142-2.889.532-3.636-.704-3.866-1.35-.13-.331-.69-1.352-1.18-1.625-.402-.216-.977-.748-.014-.762.906-.014 1.553.834 1.769 1.179 1.035 1.74 2.688 1.25 3.349.948.1-.747.402-1.25.733-1.538-2.559-.287-5.232-1.279-5.232-5.678 0-1.25.445-2.285 1.178-3.09-.115-.288-.517-1.467.115-3.048 0 0 .963-.302 3.163 1.179.92-.259 1.897-.388 2.875-.388.977 0 1.955.13 2.875.388 2.2-1.495 3.162-1.179 3.162-1.179.633 1.581.23 2.76.115 3.048.733.805 1.179 1.825 1.179 3.09 0 4.413-2.688 5.39-5.247 5.678.417.36.776 1.05.776 2.128 0 1.538-.014 2.774-.014 3.162 0 .302.216.662.79.547C20.709 21.637 24 17.324 24 12.25 24 5.896 18.854.75 12.5.75Z\"}]]}],\" \",[\"$\",\"span\",null,{\"className\":\"show-whenWide\",\"children\":\"GitHub 中文社区\"}]]}]}],[\"$\",\"div\",null,{\"className\":\"mr-3\",\"children\":[\"$\",\"$4\",null,{\"fallback\":null,\"children\":[\"$\",\"$L7\",null,{}]}]}],[\"$\",\"div\",null,{\"className\":\"mr-3 show-whenWide\",\"children\":[\"$\",\"$L6\",null,{\"className\":\"Header-link\",\"href\":\"https://forum.github-zh.com\",\"children\":\"论坛\"}]}],[\"$\",\"div\",null,{\"className\":\"mr-3 show-whenWide\",\"children\":[\"$\",\"$L6\",null,{\"className\":\"Header-link\",\"href\":\"/top\",\"children\":\"排行榜\"}]}],[\"$\",\"div\",null,{\"className\":\"mr-3 show-whenWide Header-item--full\",\"children\":[\"$\",\"$L6\",null,{\"className\":\"Header-link\",\"href\":\"/trending\",\"children\":\"趋势\"}]}],[\"$\",\"div\",null,{\"className\":\"mr-3 show-whenWide\",\"children\":[\"$\",\"$L8\",null,{}]}]]}],[[],[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]]]]}],\"$undefined\"]}],[\"$\",\"footer\",null,{\"className\":\"pt-6 pb-3 text-small\",\"style\":{\"color\":\"#666\",\"background\":\"#f6f8fa\"},\"children\":[\"$\",\"div\",null,{\"className\":\"md:container lg:container mx-auto pt-0 mt-0\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-row justify-center\",\"children\":[\"$\",\"div\",null,{\"className\":\"basis-full lg:basis-9/12\",\"children\":[\"$\",\"div\",null,{\"className\":\"px-3 flex justify-between flex-wrap-reverse\",\"children\":[[\"$\",\"p\",null,{\"className\":\"flex flex-wrap\",\"children\":[[\"$\",\"span\",null,{\"className\":\"mr-3\",\"children\":\"©2025 GitHub中文社区\"}],[\"$\",\"a\",null,{\"className\":\"mr-3\",\"href\":\"https://forum.github-zh.com/\",\"children\":\"论坛\"}],[\"$\",\"a\",null,{\"className\":\"mr-3\",\"href\":\"https://github.com/\",\"children\":\"GitHub官网\"}],[\"$\",\"$L6\",null,{\"className\":\"mr-3\",\"href\":\"/sitemap\",\"children\":\"网站地图\"}],[\"$\",\"a\",null,{\"style\":{\"display\":\"none\"},\"href\":\"https://www.github-zh.com/zh-index\",\"children\":\"GitHub官方翻译\"}]]}],[\"$\",\"div\",null,{\"children\":[\"$\",\"ul\",null,{\"className\":\"list-style-none d-flex flex-items-center mb-3 mb-sm-0 lh-condensed-ultra\",\"children\":[[\"$\",\"li\",null,{\"className\":\"mr-3\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://x.com/github\",\"className\":\"color-fg-subtle\",\"children\":[[\"$\",\"img\",null,{\"src\":\"/static/icon/x.svg\",\"height\":\"16\",\"width\":\"16\",\"className\":\"d-block\",\"loading\":\"lazy\",\"decoding\":\"async\",\"alt\":\"X icon\"}],[\"$\",\"span\",null,{\"className\":\"sr-only\",\"children\":\"GitHub on X\"}]]}]}],[\"$\",\"li\",null,{\"className\":\"mr-3\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://www.facebook.com/GitHub\",\"className\":\"color-fg-subtle\",\"children\":[[\"$\",\"img\",null,{\"src\":\"/static/icon/facebook.svg\",\"width\":\"18\",\"height\":\"18\",\"className\":\"d-block\",\"loading\":\"lazy\",\"decoding\":\"async\",\"alt\":\"Facebook icon\"}],[\"$\",\"span\",null,{\"className\":\"sr-only\",\"children\":\"GitHub on Facebook\"}]]}]}],[\"$\",\"li\",null,{\"className\":\"mr-3 flex-self-start\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://www.linkedin.com/company/github\",\"className\":\"color-fg-subtle\",\"children\":[[\"$\",\"img\",null,{\"src\":\"/static/icon/linkedin.svg\",\"width\":\"19\",\"height\":\"18\",\"className\":\"d-block\",\"loading\":\"lazy\",\"decoding\":\"async\",\"alt\":\"Linkedin icon\"}],[\"$\",\"span\",null,{\"className\":\"sr-only\",\"children\":\"GitHub on LinkedIn\"}]]}]}],[\"$\",\"li\",null,{\"className\":\"mr-3\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://www.youtube.com/github\",\"className\":\"color-fg-subtle\",\"children\":[[\"$\",\"img\",null,{\"src\":\"/static/icon/youtube.svg\",\"width\":\"23\",\"height\":\"16\",\"className\":\"d-block\",\"loading\":\"lazy\",\"decoding\":\"async\",\"alt\":\"YouTube icon\"}],[\"$\",\"span\",null,{\"className\":\"sr-only\",\"children\":\"GitHub on YouTube\"}]]}]}],[\"$\",\"li\",null,{\"className\":\"mr-3\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://www.twitch.tv/github\",\"className\":\"color-fg-subtle\",\"children\":[[\"$\",\"img\",null,{\"src\":\"/static/icon/twitch.svg\",\"width\":\"18\",\"height\":\"18\",\"className\":\"d-block\",\"loading\":\"lazy\",\"decoding\":\"async\",\"alt\":\"Twitch icon\"}],[\"$\",\"span\",null,{\"className\":\"sr-only\",\"children\":\"GitHub on Twitch\"}]]}]}],[\"$\",\"li\",null,{\"className\":\"mr-3\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://www.tiktok.com/@github\",\"className\":\"color-fg-subtle\",\"children\":[[\"$\",\"img\",null,{\"src\":\"/static/icon/tiktok.svg\",\"width\":\"18\",\"height\":\"18\",\"className\":\"d-block\",\"loading\":\"lazy\",\"decoding\":\"async\",\"alt\":\"TikTok icon\"}],[\"$\",\"span\",null,{\"className\":\"sr-only\",\"children\":\"GitHub on TikTok\"}]]}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"a\",null,{\"href\":\"https://github.com/github\",\"className\":\"color-fg-subtle\",\"children\":[[\"$\",\"img\",null,{\"src\":\"/static/icon/github.svg\",\"width\":\"20\",\"height\":\"20\",\"className\":\"d-block\",\"loading\":\"lazy\",\"decoding\":\"async\",\"alt\":\"GitHub mark\"}],[\"$\",\"span\",null,{\"className\":\"sr-only\",\"children\":\"GitHub’s organization on GitHub\"}]]}]}]]}]}]]}]}]}]}]}]]}],[\"$\",\"$L9\",null,{\"gaId\":\"G-WSRH260442\"}]]}]],\"children\":[\"$0:f:0:1:1:props:notFound:0\",[\"$\",\"html\",null,{\"suppressHydrationWarning\":true,\"lang\":\"zh-CN\",\"children\":[[\"$\",\"head\",null,{\"children\":[\"$\",\"$L2\",null,{\"data-ad-client\":\"ca-pub-9990268883331928\",\"async\":true,\"src\":\"https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js\"}]}],[\"$\",\"body\",null,{\"children\":[[\"$\",\"$L3\",null,{\"children\":[[\"$\",\"div\",null,{\"children\":[[\"$\",\"div\",null,{\"className\":\"Header d-flex flex-justify-between flex-md-justify-start\",\"style\":{\"zIndex\":1,\"position\":\"relative\"},\"children\":[[\"$\",\"div\",null,{\"className\":\"mr-3\",\"children\":[\"$\",\"$4\",null,{\"fallback\":null,\"children\":[\"$\",\"$L5\",null,{}]}]}],[\"$\",\"div\",null,{\"className\":\"Header-item mr-3\",\"children\":[\"$\",\"$L6\",null,{\"href\":\"/\",\"className\":\"Header-link f4 d-flex flex-items-center\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"aria-hidden\":\"true\",\"tabIndex\":\"$undefined\",\"focusable\":\"false\",\"aria-label\":\"$undefined\",\"aria-labelledby\":\"$undefined\",\"className\":\"octicon octicon-mark-github\",\"role\":\"$undefined\",\"viewBox\":\"0 0 24 24\",\"width\":32,\"height\":32,\"fill\":\"currentColor\",\"id\":\"$undefined\",\"style\":{\"display\":\"inline-block\",\"userSelect\":\"none\",\"verticalAlign\":\"text-bottom\",\"overflow\":\"visible\"},\"children\":[null,\"$0:f:0:1:1:props:notFound:1:props:children:1:props:children:0:props:children:0:props:children:0:props:children:1:props:children:props:children:0:props:children:1\"]}],\" \",[\"$\",\"span\",null,{\"className\":\"show-whenWide\",\"children\":\"GitHub 中文社区\"}]]}]}],[\"$\",\"div\",null,{\"className\":\"mr-3\",\"children\":[\"$\",\"$4\",null,{\"fallback\":null,\"children\":[\"$\",\"$L7\",null,{}]}]}],[\"$\",\"div\",null,{\"className\":\"mr-3 show-whenWide\",\"children\":[\"$\",\"$L6\",null,{\"className\":\"Header-link\",\"href\":\"https://forum.github-zh.com\",\"children\":\"论坛\"}]}],[\"$\",\"div\",null,{\"className\":\"mr-3 show-whenWide\",\"children\":[\"$\",\"$L6\",null,{\"className\":\"Header-link\",\"href\":\"/top\",\"children\":\"排行榜\"}]}],[\"$\",\"div\",null,{\"className\":\"mr-3 show-whenWide Header-item--full\",\"children\":[\"$\",\"$L6\",null,{\"className\":\"Header-link\",\"href\":\"/trending\",\"children\":\"趋势\"}]}],[\"$\",\"div\",null,{\"className\":\"mr-3 show-whenWide\",\"children\":[\"$\",\"$L8\",null,{}]}]]}],[\"$\",\"$La\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lb\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[\"$0:f:0:1:1:props:notFound:1:props:children:1:props:children:0:props:children:0:props:children:1:0\",[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":\"$0:f:0:1:1:props:notFound:1:props:children:1:props:children:0:props:children:0:props:children:1:1:1:props:style\",\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":\"$0:f:0:1:1:props:notFound:1:props:children:1:props:children:0:props:children:0:props:children:1:1:1:props:children:props:children:1:props:style\",\"children\":404}],[\"$\",\"div\",null,{\"style\":\"$0:f:0:1:1:props:notFound:1:props:children:1:props:children:0:props:children:0:props:children:1:1:1:props:children:props:children:2:props:style\",\"children\":[\"$\",\"h2\",null,{\"style\":\"$0:f:0:1:1:props:notFound:1:props:children:1:props:children:0:props:children:0:props:children:1:1:1:props:children:props:children:2:props:children:props:style\",\"children\":\"This page could not be found.\"}]}]]}]}]]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],[\"$\",\"$La\",null,{\"parallelRouterKey\":\"modal\",\"segmentPath\":[\"modal\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Lb\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],[\"$\",\"footer\",null,{\"className\":\"pt-6 pb-3 text-small\",\"style\":{\"color\":\"#666\",\"background\":\"#f6f8fa\"},\"children\":[\"$\",\"div\",null,{\"className\":\"md:container lg:container mx-auto pt-0 mt-0\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-row justify-center\",\"children\":[\"$\",\"div\",null,{\"className\":\"basis-full lg:basis-9/12\",\"children\":[\"$\",\"div\",null,{\"className\":\"px-3 flex justify-between flex-wrap-reverse\",\"children\":[[\"$\",\"p\",null,{\"className\":\"flex flex-wrap\",\"children\":[[\"$\",\"span\",null,{\"className\":\"mr-3\",\"children\":\"©2025 GitHub中文社区\"}],[\"$\",\"a\",null,{\"className\":\"mr-3\",\"href\":\"https://forum.github-zh.com/\",\"children\":\"论坛\"}],[\"$\",\"a\",null,{\"className\":\"mr-3\",\"href\":\"https://github.com/\",\"children\":\"GitHub官网\"}],[\"$\",\"$L6\",null,{\"className\":\"mr-3\",\"href\":\"/sitemap\",\"children\":\"网站地图\"}],[\"$\",\"a\",null,{\"style\":{\"display\":\"none\"},\"href\":\"https://www.github-zh.com/zh-index\",\"children\":\"GitHub官方翻译\"}]]}],[\"$\",\"div\",null,{\"children\":[\"$\",\"ul\",null,{\"className\":\"list-style-none d-flex flex-items-center mb-3 mb-sm-0 lh-condensed-ultra\",\"children\":[[\"$\",\"li\",null,{\"className\":\"mr-3\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://x.com/github\",\"className\":\"color-fg-subtle\",\"children\":[[\"$\",\"img\",null,{\"src\":\"/static/icon/x.svg\",\"height\":\"16\",\"width\":\"16\",\"className\":\"d-block\",\"loading\":\"lazy\",\"decoding\":\"async\",\"alt\":\"X icon\"}],[\"$\",\"span\",null,{\"className\":\"sr-only\",\"children\":\"GitHub on X\"}]]}]}],[\"$\",\"li\",null,{\"className\":\"mr-3\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://www.facebook.com/GitHub\",\"className\":\"color-fg-subtle\",\"children\":[[\"$\",\"img\",null,{\"src\":\"/static/icon/facebook.svg\",\"width\":\"18\",\"height\":\"18\",\"className\":\"d-block\",\"loading\":\"lazy\",\"decoding\":\"async\",\"alt\":\"Facebook icon\"}],[\"$\",\"span\",null,{\"className\":\"sr-only\",\"children\":\"GitHub on Facebook\"}]]}]}],[\"$\",\"li\",null,{\"className\":\"mr-3 flex-self-start\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://www.linkedin.com/company/github\",\"className\":\"color-fg-subtle\",\"children\":[[\"$\",\"img\",null,{\"src\":\"/static/icon/linkedin.svg\",\"width\":\"19\",\"height\":\"18\",\"className\":\"d-block\",\"loading\":\"lazy\",\"decoding\":\"async\",\"alt\":\"Linkedin icon\"}],[\"$\",\"span\",null,{\"className\":\"sr-only\",\"children\":\"GitHub on LinkedIn\"}]]}]}],[\"$\",\"li\",null,{\"className\":\"mr-3\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://www.youtube.com/github\",\"className\":\"color-fg-subtle\",\"children\":[[\"$\",\"img\",null,{\"src\":\"/static/icon/youtube.svg\",\"width\":\"23\",\"height\":\"16\",\"className\":\"d-block\",\"loading\":\"lazy\",\"decoding\":\"async\",\"alt\":\"YouTube icon\"}],[\"$\",\"span\",null,{\"className\":\"sr-only\",\"children\":\"GitHub on YouTube\"}]]}]}],[\"$\",\"li\",null,{\"className\":\"mr-3\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://www.twitch.tv/github\",\"className\":\"color-fg-subtle\",\"children\":[[\"$\",\"img\",null,{\"src\":\"/static/icon/twitch.svg\",\"width\":\"18\",\"height\":\"18\",\"className\":\"d-block\",\"loading\":\"lazy\",\"decoding\":\"async\",\"alt\":\"Twitch icon\"}],[\"$\",\"span\",null,{\"className\":\"sr-only\",\"children\":\"GitHub on Twitch\"}]]}]}],[\"$\",\"li\",null,{\"className\":\"mr-3\",\"children\":[\"$\",\"a\",null,{\"href\":\"https://www.tiktok.com/@github\",\"className\":\"color-fg-subtle\",\"children\":[[\"$\",\"img\",null,{\"src\":\"/static/icon/tiktok.svg\",\"width\":\"18\",\"height\":\"18\",\"className\":\"d-block\",\"loading\":\"lazy\",\"decoding\":\"async\",\"alt\":\"TikTok icon\"}],[\"$\",\"span\",null,{\"className\":\"sr-only\",\"children\":\"GitHub on TikTok\"}]]}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"a\",null,{\"href\":\"https://github.com/github\",\"className\":\"color-fg-subtle\",\"children\":[[\"$\",\"img\",null,{\"src\":\"/static/icon/github.svg\",\"width\":\"20\",\"height\":\"20\",\"className\":\"d-block\",\"loading\":\"lazy\",\"decoding\":\"async\",\"alt\":\"GitHub mark\"}],[\"$\",\"span\",null,{\"className\":\"sr-only\",\"children\":\"GitHub’s organization on GitHub\"}]]}]}]]}]}]]}]}]}]}]}]]}],[\"$\",\"$L9\",null,{\"gaId\":\"G-WSRH260442\"}]]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$c\",\"c\",{\"children\":[\"$Ld\",[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/19067771cc4d3ee8.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/13fe034d1d788ff0.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"$Le\",null,{\"children\":\"$Lf\"}]]}],{},null,false],\"modal\":[\"__DEFAULT__\",[\"$\",\"$c\",\"c\",{\"children\":[null,null,[\"$\",\"$Le\",null,{\"children\":\"$L10\"}]]}],{},null,false]},[[\"$\",\"$c\",\"l\",{\"children\":[\"$undefined\",[\"$\",\"$L11\",null,{}]]}],[],[]],false],[\"$\",\"$c\",\"h\",{\"children\":[null,[\"$\",\"$c\",\"S9a00CRAiBR54g6w9eQna\",{\"children\":[[\"$\",\"$L12\",null,{\"children\":\"$L13\"}],[\"$\",\"$L14\",null,{\"children\":\"$L15\"}],null]}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$16\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"10:null\n"])</script><script>self.__next_f.push([1,"15:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n13:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"1\",{\"children\":\"GitHub中文社区\"}],[\"$\",\"meta\",\"2\",{\"name\":\"description\",\"content\":\"重要提示原githubs.cn已无法访问，域名已变更为github-zh.com。GitHub是世界上最大的代码托管平台，超7千万开发者正在使用。GitHub中文社区，是国内领先的开源社区，是一个帮您发现GitHub上优质开源项目的地方。提供GitHub趋势，GitHub排行榜，GitHub分类检索，中文翻译等实用功能\"}]]\n"])</script><script>self.__next_f.push([1,"f:null\n"])</script><script>self.__next_f.push([1,"17:I[38717,[\"4566\",\"static/chunks/3deddded-68201c8d81665792.js\",\"504\",\"static/chunks/baf71385-d0c999ba092b5fe2.js\",\"8173\",\"static/chunks/8173-d098b821394bd879.js\",\"5805\",\"static/chunks/5805-1abbbe5bb126872b.js\",\"482\",\"static/chunks/482-9be9609530651154.js\",\"5445\",\"static/chunks/5445-da8093459f30f6a0.js\",\"5565\",\"static/chunks/5565-c0772e77e216bce8.js\",\"9435\",\"static/chunks/9435-088317c1b3cdacf0.js\",\"4584\",\"static/chunks/4584-6111dc905d81e591.js\",\"7461\",\"static/chunks/7461-439a77a375650687.js\",\"3562\",\"static/chunks/3562-eee04cad71fcedad.js\",\"5377\",\"static/chunks/5377-84470fbb5a85c869.js\",\"5632\",\"static/chunks/5632-8a88983323da71bf.js\",\"3033\",\"static/chunks/3033-8eea8d07aec5e7d5.js\",\"6054\",\"static/chunks/6054-607a988849a9c9b2.js\",\"3446\",\"static/chunks/3446-8e433dfa2fac8253.js\",\"8974\",\"static/chunks/app/page-18e0f5b7f45a1756.js\"],\"default\"]\n18:I[50139,[\"4566\",\"static/chunks/3deddded-68201c8d81665792.js\",\"504\",\"static/chunks/baf71385-d0c999ba092b5fe2.js\",\"8173\",\"static/chunks/8173-d098b821394bd879.js\",\"5805\",\"static/chunks/5805-1abbbe5bb126872b.js\",\"482\",\"static/chunks/482-9be9609530651154.js\",\"5445\",\"static/chunks/5445-da8093459f30f6a0.js\",\"5565\",\"static/chunks/5565-c0772e77e216bce8.js\",\"9435\",\"static/chunks/9435-088317c1b3cdacf0.js\",\"4584\",\"static/chunks/4584-6111dc905d81e591.js\",\"7461\",\"static/chunks/7461-439a77a375650687.js\",\"3562\",\"static/chunks/3562-eee04cad71fcedad.js\",\"5377\",\"static/chunks/5377-84470fbb5a85c869.js\",\"5632\",\"static/chunks/5632-8a88983323da71bf.js\",\"3033\",\"static/chunks/3033-8eea8d07aec5e7d5.js\",\"6054\",\"static/chunks/6054-607a988849a9c9b2.js\",\"3446\",\"static/chunks/3446-8e433dfa2fac8253.js\",\"8974\",\"static/chunks/app/page-18e0f5b7f45a1756.js\"],\"default\"]\n19:I[42973,[\"4566\",\"static/chunks/3deddded-68201c8d81665792.js\",\"504\",\"static/chunks/baf71385-d0c999ba092b5fe2.js\",\"8173\",\"static/chunks/8173-d098b821394bd879.js\",\"5805\",\"static/chunks/5805-1abbbe5bb126872b.js\",\"482\",\"static/chunks/482-9be9609530651154.js\",\"5445\",\"static/chunks/5445-da8093459f30f6a0.js\",\"5565\",\"static/chunks/5565-c0772e77e216bce8.j"])</script><script>self.__next_f.push([1,"s\",\"9435\",\"static/chunks/9435-088317c1b3cdacf0.js\",\"4584\",\"static/chunks/4584-6111dc905d81e591.js\",\"7461\",\"static/chunks/7461-439a77a375650687.js\",\"3562\",\"static/chunks/3562-eee04cad71fcedad.js\",\"5377\",\"static/chunks/5377-84470fbb5a85c869.js\",\"5632\",\"static/chunks/5632-8a88983323da71bf.js\",\"3033\",\"static/chunks/3033-8eea8d07aec5e7d5.js\",\"6054\",\"static/chunks/6054-607a988849a9c9b2.js\",\"3446\",\"static/chunks/3446-8e433dfa2fac8253.js\",\"8974\",\"static/chunks/app/page-18e0f5b7f45a1756.js\"],\"default\"]\n"])</script><script>self.__next_f.push([1,"d:[\"$\",\"div\",null,{\"style\":{\"display\":\"flex\"},\"children\":[[\"$\",\"aside\",null,{\"style\":{\"background\":\"#fff\"},\"className\":\"border-right show-whenWide\",\"children\":[\"$\",\"div\",null,{\"className\":\"sticky top-0\",\"children\":[[\"$\",\"br\",null,{}],[\"$\",\"nav-list\",null,{\"className\":\"px-1\",\"style\":{\"width\":200},\"children\":[[\"$\",\"$L6\",\"/trending\",{\"className\":\"no-underline d-block color-fg-default\",\"style\":{\"marginTop\":10},\"href\":\"/trending\",\"children\":[\"$\",\"button\",null,{\"className\":\"btn btn-block btn-invisible color-fg-default text-left\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"aria-hidden\":\"true\",\"tabIndex\":\"$undefined\",\"focusable\":\"false\",\"aria-label\":\"$undefined\",\"aria-labelledby\":\"$undefined\",\"className\":\"octicon octicon-flame\",\"role\":\"$undefined\",\"viewBox\":\"0 0 16 16\",\"width\":16,\"height\":16,\"fill\":\"currentColor\",\"id\":\"$undefined\",\"style\":{\"display\":\"inline-block\",\"userSelect\":\"none\",\"verticalAlign\":\"text-bottom\",\"overflow\":\"visible\"},\"children\":[null,[\"$\",\"path\",null,{\"d\":\"M9.533.753V.752c.217 2.385 1.463 3.626 2.653 4.81C13.37 6.74 14.498 7.863 14.498 10c0 3.5-3 6-6.5 6S1.5 13.512 1.5 10c0-1.298.536-2.56 1.425-3.286.376-.308.862 0 1.035.454C4.46 8.487 5.581 8.419 6 8c.282-.282.341-.811-.003-1.5C4.34 3.187 7.035.75 8.77.146c.39-.137.726.194.763.607ZM7.998 14.5c2.832 0 5-1.98 5-4.5 0-1.463-.68-2.19-1.879-3.383l-.036-.037c-1.013-1.008-2.3-2.29-2.834-4.434-.322.256-.63.579-.864.953-.432.696-.621 1.58-.046 2.73.473.947.67 2.284-.278 3.232-.61.61-1.545.84-2.403.633a2.79 2.79 0 0 1-1.436-.874A3.198 3.198 0 0 0 3 10c0 2.53 2.164 4.5 4.998 4.5Z\"}]]}],[\"$\",\"span\",null,{\"className\":\"ml-2 font-sans text-gray-600\",\"children\":\"趋势\"}]]}]}],[\"$\",\"$L6\",\"/awesome\",{\"className\":\"no-underline d-block color-fg-default\",\"style\":{\"marginTop\":10},\"href\":\"/awesome\",\"children\":[\"$\",\"button\",null,{\"className\":\"btn btn-block btn-invisible color-fg-default text-left\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"aria-hidden\":\"true\",\"tabIndex\":\"$undefined\",\"focusable\":\"false\",\"aria-label\":\"$undefined\",\"aria-labelledby\":\"$undefined\",\"className\":\"octicon octicon-thumbsup\",\"role\":\"$undefined\",\"viewBox\":\"0 0 16 16\",\"width\":16,\"height\":16,\"fill\":\"currentColor\",\"id\":\"$undefined\",\"style\":{\"display\":\"inline-block\",\"userSelect\":\"none\",\"verticalAlign\":\"text-bottom\",\"overflow\":\"visible\"},\"children\":[null,[\"$\",\"path\",null,{\"d\":\"M8.347.631A.75.75 0 0 1 9.123.26l.238.04a3.25 3.25 0 0 1 2.591 4.098L11.494 6h.665a3.25 3.25 0 0 1 3.118 4.167l-1.135 3.859A2.751 2.751 0 0 1 11.503 16H6.586a3.75 3.75 0 0 1-2.184-.702A1.75 1.75 0 0 1 3 16H1.75A1.75 1.75 0 0 1 0 14.25v-6.5C0 6.784.784 6 1.75 6h3.417a.25.25 0 0 0 .217-.127ZM4.75 13.649l.396.33c.404.337.914.521 1.44.521h4.917a1.25 1.25 0 0 0 1.2-.897l1.135-3.859A1.75 1.75 0 0 0 12.159 7.5H10.5a.75.75 0 0 1-.721-.956l.731-2.558a1.75 1.75 0 0 0-1.127-2.14L6.69 6.611a1.75 1.75 0 0 1-1.523.889H4.75ZM3.25 7.5h-1.5a.25.25 0 0 0-.25.25v6.5c0 .138.112.25.25.25H3a.25.25 0 0 0 .25-.25Z\"}]]}],[\"$\",\"span\",null,{\"className\":\"ml-2 font-sans text-gray-600\",\"children\":\"精选\"}]]}]}],[\"$\",\"$L6\",\"/top\",{\"className\":\"no-underline d-block color-fg-default\",\"style\":{\"marginTop\":10},\"href\":\"/top\",\"children\":[\"$\",\"button\",null,{\"className\":\"btn btn-block btn-invisible color-fg-default text-left\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"aria-hidden\":\"true\",\"tabIndex\":\"$undefined\",\"focusable\":\"false\",\"aria-label\":\"$undefined\",\"aria-labelledby\":\"$undefined\",\"className\":\"octicon octicon-trophy\",\"role\":\"$undefined\",\"viewBox\":\"0 0 16 16\",\"width\":16,\"height\":16,\"fill\":\"currentColor\",\"id\":\"$undefined\",\"style\":{\"display\":\"inline-block\",\"userSelect\":\"none\",\"verticalAlign\":\"text-bottom\",\"overflow\":\"visible\"},\"children\":[null,[\"$\",\"path\",null,{\"d\":\"M3.217 6.962A3.75 3.75 0 0 1 0 3.25v-.5C0 1.784.784 1 1.75 1h1.356c.228-.585.796-1 1.462-1h6.864c.647 0 1.227.397 1.462 1h1.356c.966 0 1.75.784 1.75 1.75v.5a3.75 3.75 0 0 1-3.217 3.712 5.014 5.014 0 0 1-2.771 3.117l.144 1.446c.005.05.03.12.114.204.086.087.217.17.373.227.283.103.618.274.89.568.285.31.467.723.467 1.226v.75h1.25a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1 0-1.5H4v-.75c0-.503.182-.916.468-1.226.27-.294.606-.465.889-.568.139-.048.266-.126.373-.227.084-.085.109-.153.114-.204l.144-1.446a5.015 5.015 0 0 1-2.77-3.117ZM4.5 1.568V5.5a3.5 3.5 0 1 0 7 0V1.568a.068.068 0 0 0-.068-.068H4.568a.068.068 0 0 0-.068.068Zm2.957 8.902-.12 1.204c-.093.925-.858 1.47-1.467 1.691a.766.766 0 0 0-.3.176c-.037.04-.07.093-.07.21v.75h5v-.75c0-.117-.033-.17-.07-.21a.766.766 0 0 0-.3-.176c-.609-.221-1.374-.766-1.466-1.69l-.12-1.204a5.064 5.064 0 0 1-1.087 0ZM13 2.5v2.872a2.25 2.25 0 0 0 1.5-2.122v-.5a.25.25 0 0 0-.25-.25H13Zm-10 0H1.75a.25.25 0 0 0-.25.25v.5c0 .98.626 1.813 1.5 2.122Z\"}]]}],[\"$\",\"span\",null,{\"className\":\"ml-2 font-sans text-gray-600\",\"children\":\"排行榜\"}]]}]}],[\"$\",\"$L6\",\"/collections\",{\"className\":\"no-underline d-block color-fg-default\",\"style\":{\"marginTop\":10},\"href\":\"/collections\",\"children\":[\"$\",\"button\",null,{\"className\":\"btn btn-block btn-invisible color-fg-default text-left\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"aria-hidden\":\"true\",\"tabIndex\":\"$undefined\",\"focusable\":\"false\",\"aria-label\":\"$undefined\",\"aria-labelledby\":\"$undefined\",\"className\":\"octicon octicon-workflow\",\"role\":\"$undefined\",\"viewBox\":\"0 0 16 16\",\"width\":16,\"height\":16,\"fill\":\"currentColor\",\"id\":\"$undefined\",\"style\":{\"display\":\"inline-block\",\"userSelect\":\"none\",\"verticalAlign\":\"text-bottom\",\"overflow\":\"visible\"},\"children\":[null,[\"$\",\"path\",null,{\"d\":\"M0 1.75C0 .784.784 0 1.75 0h3.5C6.216 0 7 .784 7 1.75v3.5A1.75 1.75 0 0 1 5.25 7H4v4a1 1 0 0 0 1 1h4v-1.25C9 9.784 9.784 9 10.75 9h3.5c.966 0 1.75.784 1.75 1.75v3.5A1.75 1.75 0 0 1 14.25 16h-3.5A1.75 1.75 0 0 1 9 14.25v-.75H5A2.5 2.5 0 0 1 2.5 11V7h-.75A1.75 1.75 0 0 1 0 5.25Zm1.75-.25a.25.25 0 0 0-.25.25v3.5c0 .138.112.25.25.25h3.5a.25.25 0 0 0 .25-.25v-3.5a.25.25 0 0 0-.25-.25Zm9 9a.25.25 0 0 0-.25.25v3.5c0 .138.112.25.25.25h3.5a.25.25 0 0 0 .25-.25v-3.5a.25.25 0 0 0-.25-.25Z\"}]]}],[\"$\",\"span\",null,{\"className\":\"ml-2 font-sans text-gray-600\",\"children\":\"索引\"}]]}]}],[\"$\",\"$L6\",\"/search\",{\"className\":\"no-underline d-block color-fg-default\",\"style\":{\"marginTop\":10},\"href\":\"/search\",\"children\":[\"$\",\"button\",null,{\"className\":\"btn btn-block btn-invisible color-fg-default text-left\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"aria-hidden\":\"true\",\"tabIndex\":\"$undefined\",\"focusable\":\"false\",\"aria-label\":\"$undefined\",\"aria-labelledby\":\"$undefined\",\"className\":\"octicon octicon-search\",\"role\":\"$undefined\",\"viewBox\":\"0 0 16 16\",\"width\":16,\"height\":16,\"fill\":\"currentColor\",\"id\":\"$undefined\",\"style\":{\"display\":\"inline-block\",\"userSelect\":\"none\",\"verticalAlign\":\"text-bottom\",\"overflow\":\"visible\"},\"children\":[null,[\"$\",\"path\",null,{\"d\":\"M10.68 11.74a6 6 0 0 1-7.922-8.982 6 6 0 0 1 8.982 7.922l3.04 3.04a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215ZM11.5 7a4.499 4.499 0 1 0-8.997 0A4.499 4.499 0 0 0 11.5 7Z\"}]]}],[\"$\",\"span\",null,{\"className\":\"ml-2 font-sans text-gray-600\",\"children\":\"搜索\"}]]}]}],[\"$\",\"$L6\",\"/real-world\",{\"className\":\"no-underline d-block color-fg-default\",\"style\":{\"marginTop\":10},\"href\":\"/real-world\",\"children\":[\"$\",\"button\",null,{\"className\":\"btn btn-block btn-invisible color-fg-default text-left\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"aria-hidden\":\"true\",\"tabIndex\":\"$undefined\",\"focusable\":\"false\",\"aria-label\":\"$undefined\",\"aria-labelledby\":\"$undefined\",\"className\":\"octicon octicon-star\",\"role\":\"$undefined\",\"viewBox\":\"0 0 16 16\",\"width\":16,\"height\":16,\"fill\":\"currentColor\",\"id\":\"$undefined\",\"style\":{\"display\":\"inline-block\",\"userSelect\":\"none\",\"verticalAlign\":\"text-bottom\",\"overflow\":\"visible\"},\"children\":[null,[\"$\",\"path\",null,{\"d\":\"M8 .25a.75.75 0 0 1 .673.418l1.882 3.815 4.21.612a.75.75 0 0 1 .416 1.279l-3.046 2.97.719 4.192a.751.751 0 0 1-1.088.791L8 12.347l-3.766 1.98a.75.75 0 0 1-1.088-.79l.72-4.194L.818 6.374a.75.75 0 0 1 .416-1.28l4.21-.611L7.327.668A.75.75 0 0 1 8 .25Zm0 2.445L6.615 5.5a.75.75 0 0 1-.564.41l-3.097.45 2.24 2.184a.75.75 0 0 1 .216.664l-.528 3.084 2.769-1.456a.75.75 0 0 1 .698 0l2.77 1.456-.53-3.084a.75.75 0 0 1 .216-.664l2.24-2.183-3.096-.45a.75.75 0 0 1-.564-.41L8 2.694Z\"}]]}],[\"$\",\"span\",null,{\"className\":\"ml-2 font-sans text-gray-600\",\"children\":\"实战项目\"}]]}]}],[\"$\",\"$L6\",\"/top-developers\",{\"className\":\"no-underline d-block color-fg-default\",\"style\":{\"marginTop\":10},\"href\":\"/top-developers\",\"children\":[\"$\",\"button\",null,{\"className\":\"btn btn-block btn-invisible color-fg-default text-left\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"aria-hidden\":\"true\",\"tabIndex\":\"$undefined\",\"focusable\":\"false\",\"aria-label\":\"$undefined\",\"aria-labelledby\":\"$undefined\",\"className\":\"octicon octicon-trophy\",\"role\":\"$undefined\",\"viewBox\":\"0 0 16 16\",\"width\":16,\"height\":16,\"fill\":\"currentColor\",\"id\":\"$undefined\",\"style\":{\"display\":\"inline-block\",\"userSelect\":\"none\",\"verticalAlign\":\"text-bottom\",\"overflow\":\"visible\"},\"children\":[null,\"$d:props:children:0:props:children:props:children:1:props:children:2:props:children:props:children:0:props:children:1\"]}],[\"$\",\"span\",null,{\"className\":\"ml-2 font-sans text-gray-600\",\"children\":\"开发者榜单\"}]]}]}]]}]]}]}],[\"$\",\"div\",null,{\"className\":\"flex-auto\",\"children\":[[\"$\",\"section\",null,{\"style\":{\"background\":\"#f6f8fa\"},\"className\":\"pb-4\",\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"div\",null,{\"className\":\"border-bottom mb-6\",\"style\":{\"background\":\"#fff\"},\"children\":[\"$\",\"div\",null,{\"className\":\"container-lg p-responsive text-center py-6\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"h3\",\"children\":\"GitHub 中文社区\"}],[\"$\",\"p\",null,{\"className\":\"color-fg-subtle col-md-8 mx-auto\",\"children\":\"GitHub 是世界上最大的代码托管平台，超 1 亿开发者正在使用。GitHub 中文社区是一个致力于分享和传播 GitHub 上优质开源项目的中文社区平台。\"}]]}]}],[\"$\",\"div\",null,{\"className\":\"md:container lg:container mx-auto pt-0 mt-0\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-row justify-center\",\"children\":[\"$\",\"div\",null,{\"className\":\"basis-full lg:basis-9/12\",\"children\":[\"$\",\"div\",null,{\"className\":\"p-1 grid grid-cols-2 md:grid-cols-3 gap-1\",\"children\":[[\"$\",\"div\",\"0\",{\"children\":[\"$\",\"div\",null,{\"className\":\"Box anim-hover-grow p-3 color-fg-accent\",\"style\":{\"height\":\"100%\"},\"children\":[\"$\",\"$L6\",null,{\"href\":\"/getting-started\",\"style\":{\"display\":\"block\",\"textDecoration\":\"none\"},\"children\":[[\"$\",\"p\",null,{\"className\":\"f4 text-center\",\"children\":\"新手训练营\"}],[\"$\",\"p\",null,{\"className\":\"f5 text-center color-fg-subtle mb-0\",\"children\":\"适合所有人的交互式课程\"}]]}]}]}],[\"$\",\"div\",\"1\",{\"children\":[\"$\",\"div\",null,{\"className\":\"Box anim-hover-grow p-3 color-fg-accent\",\"style\":{\"height\":\"100%\"},\"children\":[\"$\",\"$L6\",null,{\"href\":\"/trending\",\"style\":{\"display\":\"block\",\"textDecoration\":\"none\"},\"children\":[[\"$\",\"p\",null,{\"className\":\"f4 text-center\",\"children\":\"GitHub 趋势\"}],[\"$\",\"p\",null,{\"className\":\"f5 text-center color-fg-subtle mb-0\",\"children\":\"查看当前流行和历史趋势\"}]]}]}]}],[\"$\",\"div\",\"2\",{\"children\":[\"$\",\"div\",null,{\"className\":\"Box anim-hover-grow p-3 color-fg-accent\",\"style\":{\"height\":\"100%\"},\"children\":[\"$\",\"$L6\",null,{\"href\":\"/top\",\"style\":{\"display\":\"block\",\"textDecoration\":\"none\"},\"children\":[[\"$\",\"p\",null,{\"className\":\"f4 text-center\",\"children\":\"排行榜\"}],[\"$\",\"p\",null,{\"className\":\"f5 text-center color-fg-subtle mb-0\",\"children\":\"查看 GitHub 上最受欢迎的开源项目\"}]]}]}]}],[\"$\",\"div\",\"3\",{\"children\":[\"$\",\"div\",null,{\"className\":\"Box anim-hover-grow p-3 color-fg-accent\",\"style\":{\"height\":\"100%\"},\"children\":[\"$\",\"$L6\",null,{\"href\":\"/awesome\",\"style\":{\"display\":\"block\",\"textDecoration\":\"none\"},\"children\":[[\"$\",\"p\",null,{\"className\":\"f4 text-center\",\"children\":\"GitHub 精选\"}],[\"$\",\"p\",null,{\"className\":\"f5 text-center color-fg-subtle mb-0\",\"children\":\"精选优质开源项目和学习资源\"}]]}]}]}],[\"$\",\"div\",\"4\",{\"children\":[\"$\",\"div\",null,{\"className\":\"Box anim-hover-grow p-3 color-fg-accent\",\"style\":{\"height\":\"100%\"},\"children\":[\"$\",\"$L6\",null,{\"href\":\"https://github.com/\",\"style\":{\"display\":\"block\",\"textDecoration\":\"none\"},\"children\":[[\"$\",\"p\",null,{\"className\":\"f4 text-center\",\"children\":\"GitHub 官网\"}],[\"$\",\"p\",null,{\"className\":\"f5 text-center color-fg-subtle mb-0\",\"children\":\"GitHub 英文官网入口\"}]]}]}]}],[\"$\",\"div\",\"5\",{\"children\":[\"$\",\"div\",null,{\"className\":\"Box anim-hover-grow p-3 color-fg-accent\",\"style\":{\"height\":\"100%\"},\"children\":[\"$\",\"$L6\",null,{\"href\":\"https://forum.github-zh.com/\",\"style\":{\"display\":\"block\",\"textDecoration\":\"none\"},\"children\":[[\"$\",\"p\",null,{\"className\":\"f4 text-center\",\"children\":\"论坛\"}],[\"$\",\"p\",null,{\"className\":\"f5 text-center color-fg-subtle mb-0\",\"children\":\"开发者论坛\"}]]}]}]}]]}]}]}]}]]}]}],[\"$\",\"section\",null,{\"style\":{\"background\":\"#f6f8fa\"},\"className\":\"mb-4\",\"children\":[\"$\",\"$L17\",null,{\"preloadedQuery\":{\"params\":{\"cacheID\":\"0b18408189472d6003602d58515fc7fa\",\"id\":null,\"metadata\":{},\"name\":\"pageHomeQuery\",\"operationKind\":\"query\",\"text\":\"query pageHomeQuery(\\n  $locale: String\\n) {\\n  chineseTrends: trending(range: \\\"daily\\\", cn: true) {\\n    ...TrendsFragment\\n  }\\n  dailyTrends: trending(range: \\\"daily\\\", locale: $locale) {\\n    ...TrendsFragment\\n  }\\n}\\n\\nfragment RepoCardFragment on Project {\\n  id\\n  name\\n  fullName\\n  sourceUrl\\n  starCount\\n  forkCount\\n  description\\n  remark\\n  lang\\n  screenshot\\n  url\\n  homepageUrl\\n  category {\\n    name\\n    screenName\\n    id\\n  }\\n  tags {\\n    id\\n    name\\n    displayName\\n  }\\n  createAt\\n  updateAt\\n  user {\\n    name\\n    remark\\n    poweredBy\\n    isVerified\\n    id\\n  }\\n  chinaFlag\\n  commentCount\\n  isArchived\\n  status\\n}\\n\\nfragment TrendsFragment on Trends {\\n  dateRange\\n  issueId\\n  updateAt\\n  projects {\\n    ...RepoCardFragment\\n    id\\n  }\\n}\\n\"},\"variables\":{\"locale\":\"zh\"},\"response\":{\"data\":{\"chineseTrends\":{\"dateRange\":\"daily\",\"issueId\":25072808,\"updateAt\":\"28 Jul 2025 08:00:19 GMT\",\"projects\":[{\"id\":\"cHJvamVjdDo2NDNkZTc2MGI4MzFiZmY0YzQwMThjYzQ=\",\"name\":\"TikTokDownloader\",\"fullName\":\"JoeanAmier/TikTokDownloader\",\"sourceUrl\":\"https://github.com/JoeanAmier/TikTokDownloader\",\"starCount\":11014,\"forkCount\":1864,\"description\":\"TikTok 发布/喜欢/合辑/直播/视频/图集/音乐；抖音发布/喜欢/收藏/收藏夹/视频/图集/实况/直播/音乐/合集/评论/账号/搜索/热榜数据采集工具/下载工具\",\"remark\":null,\"lang\":\"Python\",\"screenshot\":null,\"url\":\"/projects/549007739-tiktokdownloader\",\"homepageUrl\":\"https://discord.com/invite/ZYtmgKud9Y\",\"category\":null,\"tags\":[{\"id\":\"dGFnOjg3YTM4OTk4MjI3Y2JiYzIzZGNhZDUxY2Q3Zjc2YWIy\",\"name\":\"douyin\",\"displayName\":null},{\"id\":\"dGFnOjIzZWVlYjQzNDdiZGQyNmJmYzZiN2VlOWEzYjc1NWRk\",\"name\":\"python\",\"displayName\":\"Python\"},{\"id\":\"dGFnOjYyMTFhNTExMmMzNWJkYzY0NjMyOGM0Y2U4OGEzMGZk\",\"name\":\"tiktok\",\"displayName\":\"TikTok\"},{\"id\":\"dGFnOjhhNWRhNTJlZDEyNjQ0N2QzNTllNzBjMDU3MjFhOGFh\",\"name\":\"api\",\"displayName\":\"API\"},{\"id\":\"dGFnOjYyOGNiNTY3NWZmNTI0ZjNlNzE5YjdhYTJlODhmZTNm\",\"name\":\"csv\",\"displayName\":\"CSV\"},{\"id\":\"dGFnOjc4NmQzNDhlOTU0NDgxMjFkYzViNzM4N2UyMmU0ZmZm\",\"name\":\"sqlite\",\"displayName\":\"SQLite\"},{\"id\":\"dGFnOmJmM2UyOTI0ZGNiYTFjNTJkYTAxYjVlZGExMTExYjJi\",\"name\":\"xlsx\",\"displayName\":null},{\"id\":\"dGFnOmUyMDZhNTRlOTc2OTBjY2U1MGNjODcyZGQ3MGVlODk2\",\"name\":\"linux\",\"displayName\":\"Linux\"},{\"id\":\"dGFnOjQzYjlkOGVhMThjNDhjM2E2NGM0ZTM3MzM4ZmM2Njhm\",\"name\":\"macos\",\"displayName\":\"macOS\"},{\"id\":\"dGFnOjBmNDEzN2VkMTUwMmI1MDQ1ZDYwODNhYTI1OGI1YzQy\",\"name\":\"windows\",\"displayName\":\"Windows\"},{\"id\":\"dGFnOjdiZWQ0NmM1YzYxYzBhYzYyNWNlYmY4YTk5MjJjYzQ4\",\"name\":\"ffmpeg\",\"displayName\":\"FFmpeg\"},{\"id\":\"dGFnOjhmYzkxMDY0MDQxNDE5N2JhNjUwZDZmOWUxMjRjN2Zk\",\"name\":\"httpx\",\"displayName\":null},{\"id\":\"dGFnOmNmMWU4YzE0ZTU0NTA1ZjYwYWExMGNlYjhkNWQ4YWIz\",\"name\":\"server\",\"displayName\":\"Server\"},{\"id\":\"dGFnOjlmY2I4NGI0ZmUxMTczOThhOTJlMDY3ZWI2NGEwMjQz\",\"name\":\"abogus\",\"displayName\":null},{\"id\":\"dGFnOmQ0NzIxYTZiYWI4YzdkZWU3OGNjMjVmN2ZhMTU0YmZj\",\"name\":\"downloader\",\"displayName\":null},{\"id\":\"dGFnOjM1YjBlOTY0ZWRlMTRkNDY1YzcyOWFjMjBiMDU2OTY5\",\"name\":\"pyinstaller\",\"displayName\":null},{\"id\":\"dGFnOjJmMmUwYzE3NmY4Y2RjYmU0MTVmMzVjNjQxM2Q5OGRh\",\"name\":\"mstoken\",\"displayName\":null},{\"id\":\"dGFnOmM5MDVmNDI0YzU5YzIxOGMzMzk3ZWZjZTY5NzYyYzU0\",\"name\":\"ttwid\",\"displayName\":null},{\"id\":\"dGFnOjA1YjYwNTNjNDFhMjEzMGFmZDZmYzNiMTU4YmRhNGU2\",\"name\":\"docker\",\"displayName\":\"Docker\"},{\"id\":\"dGFnOmU3ZjRmOGJkMjQ2YzIzNTQxODI4MGQxZjEyNGUxNGYw\",\"name\":\"rich\",\"displayName\":null}],\"createAt\":\"2022-10-10T14:27:36Z\",\"updateAt\":\"2025-07-21T10:12:25Z\",\"user\":null,\"chinaFlag\":1,\"commentCount\":0,\"isArchived\":false,\"status\":0},{\"id\":\"cHJvamVjdDo2MDExN2ZmZGZlYjJkZWI0NGQ2OGY1Yzk=\",\"name\":\"OI-wiki\",\"fullName\":\"OI-wiki/OI-wiki\",\"sourceUrl\":\"https://github.com/OI-wiki/OI-wiki\",\"starCount\":23715,\"forkCount\":4335,\"description\":\":star2: Wiki of OI / ICPC for everyone. （某大型游戏线上攻略，内含炫酷算术魔法）\",\"remark\":null,\"lang\":\"TypeScript\",\"screenshot\":null,\"url\":\"/projects/140516736-oi-wiki\",\"homepageUrl\":\"https://oi-wiki.org\",\"category\":{\"name\":\"algorithm\",\"screenName\":\"算法刷题\",\"id\":\"Y2F0ZWdvcnk6NWY3ZGVhOGZjOTdhYjEyODUwMzJhYjBh\"},\"tags\":[{\"id\":\"dGFnOmEyZTYzZWUwMTQwMWFhZWNhNzhiZTAyM2RmYmI4YzU5\",\"name\":\"oi\",\"displayName\":null},{\"id\":\"dGFnOmM1MzMwMzRjY2NmYjU0YWU2NTdmNDRjOTA5MDgyMzBk\",\"name\":\"oi-handbook\",\"displayName\":null},{\"id\":\"dGFnOmExZjQ0ZDU2ZWYzYzYyMzdlZmRjYmNlMzdlN2MyMDJk\",\"name\":\"data-structures\",\"displayName\":\"数据结构\"},{\"id\":\"dGFnOjY2MjcwNzA3NDI0YTcyOWMzZTU1N2ZjZWIwM2Y0NWM5\",\"name\":\"algorithms\",\"displayName\":\"算法\"},{\"id\":\"dGFnOjk3MTFjM2I4NjIxYTJmMDNhODkzYmJjZDM3N2FmZTcx\",\"name\":\"competitive-programming\",\"displayName\":null},{\"id\":\"dGFnOjJkODk5ZjhkMTYzNTAyYjEyZWI0YTYwMDY5ZjgwYzFj\",\"name\":\"icpc\",\"displayName\":null},{\"id\":\"dGFnOjkyMDU0YzRjYjE5YjlkMDMzMDc1MjdiMjkzYjhiNTMw\",\"name\":\"acm-icpc\",\"displayName\":null},{\"id\":\"dGFnOmMzOWQ4MTcyZWIzNzU4ZWNlZDRiZDFmM2IxY2Q0Mjkx\",\"name\":\"acm-icpc-handbook\",\"displayName\":null},{\"id\":\"dGFnOjk0MzYzZjE4MTc5ZDhhYWQ1OWNkM2JmNWY0NTY3ZjI4\",\"name\":\"icpc-handbook\",\"displayName\":null},{\"id\":\"dGFnOjcwYjA2NDJmMmQyMmYwYTRhYThmOTg4Y2IxZDZiYmZi\",\"name\":\"icpc-training\",\"displayName\":null},{\"id\":\"dGFnOmMyNGQzZDFlM2U3MWRkNWRkNTk5MzUxYjc3ZjcyMDFk\",\"name\":\"oi-training\",\"displayName\":null},{\"id\":\"dGFnOjJiMzE3Y2QyZTVhNWQ3YmJlNzQ3MWI0MTljMjNlZWMw\",\"name\":\"hacktoberfest\",\"displayName\":\"Hacktoberfest\"}],\"createAt\":\"2018-07-11T03:28:14Z\",\"updateAt\":\"2025-07-28T07:58:40Z\",\"user\":null,\"chinaFlag\":1,\"commentCount\":0,\"isArchived\":false,\"status\":0},{\"id\":\"cHJvamVjdDo2M2NmYzg0ZjIxMDEyZGIxZGRjODU1NzQ=\",\"name\":\"Android-DataBackup\",\"fullName\":\"XayahSuSuSu/Android-DataBackup\",\"sourceUrl\":\"https://github.com/XayahSuSuSu/Android-DataBackup\",\"starCount\":5313,\"forkCount\":206,\"description\":\"DataBackup for Android 7.0+\",\"remark\":\"一款运行在Android 8+的免费开源的数据备份应用\",\"lang\":\"Kotlin\",\"screenshot\":null,\"url\":\"/projects/430540203-android-databackup\",\"homepageUrl\":\"https://DataBackupOfficial.github.io\",\"category\":{\"name\":\"android\",\"screenName\":\"安卓\",\"id\":\"Y2F0ZWdvcnk6NWY3NTE1ODEwZWE0OTUzZTEwYWY0YWYw\"},\"tags\":[{\"id\":\"dGFnOjQwMjA1MWY0YmUwY2MzYWFkMzNiY2YzYWMzZDY1MzJi\",\"name\":\"backup\",\"displayName\":null},{\"id\":\"dGFnOjYzYTlmMGVhN2JiOTgwNTA3OTZiNjQ5ZTg1NDgxODQ1\",\"name\":\"root\",\"displayName\":null},{\"id\":\"dGFnOmMzMWIzMjM2NGNlMTljYThmY2QxNTBhNDE3ZWNjZTU4\",\"name\":\"android\",\"displayName\":\"Android\"},{\"id\":\"dGFnOjE2OWI3OTEwZGNjYTM3MzE3NzNlMTMwYjBjZTFhYjVi\",\"name\":\"zstd\",\"displayName\":null},{\"id\":\"dGFnOjEzYjNmM2U3NmVkMDE4YTEwNWJkZWFjNzdhYTg5Njdl\",\"name\":\"compose\",\"displayName\":null}],\"createAt\":\"2021-11-22T02:42:21Z\",\"updateAt\":\"2025-07-25T16:06:11Z\",\"user\":null,\"chinaFlag\":1,\"commentCount\":0,\"isArchived\":false,\"status\":0},{\"id\":\"cHJvamVjdDo2MmE3MThjMTA1YmI3MWU2YWMyNDQ3YWU=\",\"name\":\"nekoray\",\"fullName\":\"MatsuriDayo/nekoray\",\"sourceUrl\":\"https://github.com/MatsuriDayo/nekoray\",\"starCount\":14795,\"forkCount\":1405,\"description\":\"不再维护，自寻替代品。 Qt based cross-platform GUI proxy configuration manager (backend: sing-box)\",\"remark\":null,\"lang\":\"C++\",\"screenshot\":null,\"url\":\"/projects/488177011-nekoray\",\"homepageUrl\":\"https://matsuridayo.github.io/\",\"category\":null,\"tags\":[],\"createAt\":\"2022-05-03T11:12:00Z\",\"updateAt\":\"2024-12-12T08:25:39Z\",\"user\":null,\"chinaFlag\":1,\"commentCount\":0,\"isArchived\":true,\"status\":0},{\"id\":\"cHJvamVjdDo2MzkwNzIzZGI3MGUwMWI3MmM3ODAwY2M=\",\"name\":\"live\",\"fullName\":\"fanmingming/live\",\"sourceUrl\":\"https://github.com/fanmingming/live\",\"starCount\":26072,\"forkCount\":3915,\"description\":\"✯ 可直连访问的电视/广播图标库与相关工具项目 ✯ 🔕 永久免费 直连访问 完整开源 不断完善的台标 支持IPv4/IPv6双栈访问 🔕\",\"remark\":null,\"lang\":\"JavaScript\",\"screenshot\":null,\"url\":\"/projects/566931318-live\",\"homepageUrl\":\"https://live.fanmingming.com/\",\"category\":{\"name\":\"iptv\",\"screenName\":\"IPTV\",\"id\":\"Y2F0ZWdvcnk6NjczNjFjMDA1NTQwY2NiNzQ3NDYxZWFl\"},\"tags\":[{\"id\":\"dGFnOmEzOThmYjc3ZGY3NmU2MTUzZGY1N2NkNjVmZDBhN2M1\",\"name\":\"radio\",\"displayName\":null},{\"id\":\"dGFnOmUyZTg0MGMwZWE0YWJkOGFmNzFmMWE2N2E1NTNiYmU1\",\"name\":\"iptv\",\"displayName\":null},{\"id\":\"dGFnOmNjMzE0Y2JjNmFlNzFjMDcyNDM5MGViNDUwYmI5Njlk\",\"name\":\"ipv6\",\"displayName\":null},{\"id\":\"dGFnOmQwZGJlOTE1MDkxZDQwMGJkOGVlN2YyN2YwNzkxMzAz\",\"name\":\"live\",\"displayName\":null},{\"id\":\"dGFnOmIzY2ZlZmVlYTljMmY1MzFjMzZkNmZlNjdjZDk2YzFi\",\"name\":\"m3u\",\"displayName\":null},{\"id\":\"dGFnOmM5YTFmZGFjNmUwODJkZDg5ZTcxNzMyNDRmMzRkN2Iz\",\"name\":\"tv\",\"displayName\":null},{\"id\":\"dGFnOjc0NDczOWE1NGM2MTkxZTM1NzE0YzdkZDdiYmYyMjY5\",\"name\":\"m3u8\",\"displayName\":null},{\"id\":\"dGFnOjc5NDY0MjEyYWZiN2ZkNmMzODY5OWQwNjE3ZWFlZGVi\",\"name\":\"television\",\"displayName\":null},{\"id\":\"dGFnOjhhN2Q3YmEyODhjYTBmMGVhMWVjZjk3NWIwMjZlOGUx\",\"name\":\"china\",\"displayName\":null},{\"id\":\"dGFnOjRhODBmYmNhNGU1NjcxMjEwODg1Y2NhNzVkYTI1Njc5\",\"name\":\"epg\",\"displayName\":null},{\"id\":\"dGFnOjQ4NjZkNjM1ZWE3YjBlZDhlMjQ1MmRhODk3MTQzNTI4\",\"name\":\"mp4\",\"displayName\":null},{\"id\":\"dGFnOjBlYTdkZDRkYTM3MmYxYTY4YTVkZGEzYjlmYzdlMmU4\",\"name\":\"converter\",\"displayName\":null},{\"id\":\"dGFnOmM3ODI0ZjNkNGQ1ZjdiMmYyMmQwMzQ3NThjMWU5NDU0\",\"name\":\"txt\",\"displayName\":null},{\"id\":\"dGFnOmEwNmJlMjExZWUxYjk0OWIwZGM4Y2VmOTJhNDM3M2E5\",\"name\":\"workers\",\"displayName\":null}],\"createAt\":\"2022-11-16T18:03:31Z\",\"updateAt\":\"2025-07-28T06:27:07Z\",\"user\":null,\"chinaFlag\":1,\"commentCount\":0,\"isArchived\":false,\"status\":0},{\"id\":\"cHJvamVjdDozNDRkYmZmNGZiNWJjZjliM2Q5YWIzZWRkYjI5MGIwZg==\",\"name\":\"lede\",\"fullName\":\"coolsnowwolf/lede\",\"sourceUrl\":\"https://github.com/coolsnowwolf/lede\",\"starCount\":30867,\"forkCount\":19581,\"description\":\"Lean's LEDE source\",\"remark\":\"如何编译自己需要的 OpenWrt 固件\",\"lang\":\"C\",\"screenshot\":null,\"url\":\"/projects/102578345-lede\",\"homepageUrl\":\"\",\"category\":null,\"tags\":[{\"id\":\"dGFnOjFlY2ExMWUyMGQ0NTQ1ODBkMzBlOWU4OTc1NGE3ZDcw\",\"name\":\"lede\",\"displayName\":null},{\"id\":\"dGFnOmY4ZGJiYmRiM2I4MGI0ZjE3MGE4MjcyOTc4ZjU3OWVi\",\"name\":\"lua\",\"displayName\":\"Lua\"},{\"id\":\"dGFnOmIzMTIzNzRiYmQwZmI1MmE1MmQxZjZiMTBmMDhhMmQw\",\"name\":\"openwrt-package\",\"displayName\":null},{\"id\":\"dGFnOjIwZWUyN2IwYmRkNTRmN2JiZjMwZDY0ZDVlZTlhYWQ4\",\"name\":\"openwrt-zh-cn\",\"displayName\":null},{\"id\":\"dGFnOjAxOTIwODYxMmNkOGRmZDYxOGE3YmIwMDYwZGJmNmVj\",\"name\":\"openwrt-feed\",\"displayName\":null}],\"createAt\":\"2017-09-06T07:39:03Z\",\"updateAt\":\"2025-07-27T15:04:44Z\",\"user\":{\"name\":\"coolsnowwolf\",\"remark\":null,\"poweredBy\":null,\"isVerified\":null,\"id\":\"31687149\"},\"chinaFlag\":1,\"commentCount\":0,\"isArchived\":false,\"status\":0},{\"id\":\"cHJvamVjdDo1ZmJmNmU5Y2Y5YWRjYjYzZWZiNzc4MDc=\",\"name\":\"iCSS\",\"fullName\":\"chokcoco/iCSS\",\"sourceUrl\":\"https://github.com/chokcoco/iCSS\",\"starCount\":20482,\"forkCount\":2062,\"description\":\"不止于 CSS\",\"remark\":null,\"lang\":\"TypeScript\",\"screenshot\":null,\"url\":\"/projects/68592809-icss\",\"homepageUrl\":\"https://icss-dun.vercel.app\",\"category\":null,\"tags\":[{\"id\":\"dGFnOmM3YTYyOGNiYTIyZTI4ZWIxN2I1ZjVjNmFlMmEyNjZh\",\"name\":\"css\",\"displayName\":\"CSS\"},{\"id\":\"dGFnOjY3ZGQ2NGM2NTEyNzA1MjQ5NjFkYTdhNjY4NjAwOGJh\",\"name\":\"css3\",\"displayName\":\"CSS\"}],\"createAt\":\"2016-09-19T09:55:32Z\",\"updateAt\":\"2025-07-18T02:10:32Z\",\"user\":{\"name\":\"Coco\",\"remark\":null,\"poweredBy\":null,\"isVerified\":null,\"id\":\"8554143\"},\"chinaFlag\":1,\"commentCount\":0,\"isArchived\":false,\"status\":0},{\"id\":\"cHJvamVjdDo2Mzk1YjgzYmMwOWZmZmQwMzVlMWYyYTY=\",\"name\":\"FileCodeBox\",\"fullName\":\"vastsa/FileCodeBox\",\"sourceUrl\":\"https://github.com/vastsa/FileCodeBox\",\"starCount\":6901,\"forkCount\":800,\"description\":\"文件快递柜-匿名口令分享文本，文件，像拿快递一样取文件（FileCodeBox - File Express Cabinet - Anonymous Passcode Sharing Text, Files, Like Taking Express Delivery for Files）\",\"remark\":null,\"lang\":\"Python\",\"screenshot\":null,\"url\":\"/projects/576247474-filecodebox\",\"homepageUrl\":\"https://fcb-docs.aiuo.net/\",\"category\":null,\"tags\":[{\"id\":\"dGFnOjIzZWVlYjQzNDdiZGQyNmJmYzZiN2VlOWEzYjc1NWRk\",\"name\":\"python\",\"displayName\":\"Python\"},{\"id\":\"dGFnOjI5NGRlMzU1N2Q5ZDAwYjNkMmQ4YTFlNmFhYjAyOGNm\",\"name\":\"anonymous\",\"displayName\":null},{\"id\":\"dGFnOjBkMzJiY2VkOTFhYTVjMmVlNTY5NmZjNzk5NTM3MGFl\",\"name\":\"fastapi\",\"displayName\":\"FastAPI\"},{\"id\":\"dGFnOjM5YWIzMmM1YWViNTZjOWY1YWUxN2YwNzNjZTMxMDIz\",\"name\":\"tool\",\"displayName\":\"工具\"},{\"id\":\"dGFnOmRjNmNjZWU2MDE3MWU3MTBlMjU1NGY1OTA0MzBkODc5\",\"name\":\"filecodebox\",\"displayName\":null},{\"id\":\"************************************************\",\"name\":\"vue\",\"displayName\":\"Vue.js\"}],\"createAt\":\"2022-12-09T11:10:08Z\",\"updateAt\":\"2025-07-04T16:02:10Z\",\"user\":null,\"chinaFlag\":1,\"commentCount\":0,\"isArchived\":false,\"status\":0},{\"id\":\"cHJvamVjdDo2MjhhMTdjMmYwYWU1ODU1OGZmYzE2YjI=\",\"name\":\"Shadowrocket-ADBlock-Rules-Forever\",\"fullName\":\"Johnshall/Shadowrocket-ADBlock-Rules-Forever\",\"sourceUrl\":\"https://github.com/Johnshall/Shadowrocket-ADBlock-Rules-Forever\",\"starCount\":16341,\"forkCount\":1024,\"description\":\"提供多款 Shadowrocket 规则，拥有强劲的广告过滤功能。每日 8 时重新构建规则。\",\"remark\":null,\"lang\":null,\"screenshot\":null,\"url\":\"/projects/435427730-shadowrocket-adblock-rules-forever\",\"homepageUrl\":\"https://johnshall.github.io/Shadowrocket-ADBlock-Rules-Forever/\",\"category\":null,\"tags\":[{\"id\":\"dGFnOmZiNDJkNGQ4NTY4ZTQwMWFkMDBmYjNlY2Q2YzI3YmEw\",\"name\":\"shadowrocket\",\"displayName\":null},{\"id\":\"dGFnOjQzMTM4N2ViNzI2MmUxY2ZjNzliMTI1ZWI4YTY3YzYw\",\"name\":\"proxy\",\"displayName\":null},{\"id\":\"dGFnOmZjYzY0N2Y2OGRlNWE1ZjhlM2FhZjZkYzNlNzZkYTdi\",\"name\":\"gfw\",\"displayName\":null},{\"id\":\"dGFnOmI5ZmE5ODU0ZWM0ZDhkMDRlODIzMjE3YTJhNGY4ZjUy\",\"name\":\"v2ray\",\"displayName\":null},{\"id\":\"dGFnOjhhOTM5OWMxMmJiYmRhNzBhNTU1ZmNkNDk3MGM5NmIz\",\"name\":\"adblock\",\"displayName\":null}],\"createAt\":\"2021-12-06T09:06:28Z\",\"updateAt\":\"2025-07-27T23:02:20Z\",\"user\":null,\"chinaFlag\":1,\"commentCount\":0,\"isArchived\":false,\"status\":0},{\"id\":\"cHJvamVjdDo2MmQxNDhjMGI1N2IxMjdmNjc1ZTE4ODE=\",\"name\":\"eSearch\",\"fullName\":\"xushengfeng/eSearch\",\"sourceUrl\":\"https://github.com/xushengfeng/eSearch\",\"starCount\":5798,\"forkCount\":439,\"description\":\"截屏 离线OCR 搜索翻译 以图搜图 贴图 录屏 万向滚动截屏 屏幕翻译   Screenshot  Offline OCR   Search   Translate   Search for picture   Paste the picture on the screen   Screen recorder   Omnidirectional scrolling screenshot   Screen translator  支持Windows Linux macOS\",\"remark\":null,\"lang\":\"TypeScript\",\"screenshot\":\"https://repository-images.githubusercontent.com/414027423/cde018f1-76e4-4285-a3d0-5c1e6aedbff4\",\"url\":\"/projects/414027423-esearch\",\"homepageUrl\":\"https://esearch-app.netlify.app/\",\"category\":null,\"tags\":[{\"id\":\"dGFnOjYyYzkyYmE1ODVmNzRlY2RiZWY0YzQ0OThhNDM4OTg0\",\"name\":\"screenshot\",\"displayName\":null},{\"id\":\"dGFnOjFjNjAxZDdkODAxMjY4ZDc3MmU4NzY0ODE4MDVlYzhi\",\"name\":\"screen-capture\",\"displayName\":null},{\"id\":\"dGFnOmJiYzRkZjQwNDdiYjkxZWY1MTVjZTAzYTYzMTdmZDY1\",\"name\":\"clipboard\",\"displayName\":null},{\"id\":\"dGFnOjA2YTk0M2M1OWYzM2EzNGJiNTkyNGFhZjcyY2QyOTk1\",\"name\":\"search\",\"displayName\":null},{\"id\":\"dGFnOmU5MzdkN2ExNjE0NThmYjMxNGNjYmJjZTk0MTUwZTIy\",\"name\":\"ocr\",\"displayName\":\"OCR\"},{\"id\":\"dGFnOjZkMTU2MDIzZjA1ODYyM2NjZjk5ODU0OTA2YjU4Mzkw\",\"name\":\"color-picker\",\"displayName\":null},{\"id\":\"dGFnOjYzMTJjYjNiM2IwNTM1OTE4ODcyY2EzN2ZlM2IwNGE4\",\"name\":\"live-text\",\"displayName\":null},{\"id\":\"dGFnOjcyYjVjMGM5NjczOWUyYmJmZjBmYzdjNWVjOTlkYWUw\",\"name\":\"cross-platform\",\"displayName\":null},{\"id\":\"dGFnOmQ0ZTE1OTM0YzA1Nzc4NDA4ZDczYzNiMDZiYWIyNGY2\",\"name\":\"image-editing\",\"displayName\":null},{\"id\":\"dGFnOjgyNzJhZDY1NWY1NTA3Y2JmNTdkYmI1YmI5NDUwYzQ2\",\"name\":\"image-editor\",\"displayName\":null},{\"id\":\"dGFnOjFiODU2MjM0MzExNjlmZWRjOWEyMGVjZjI0ZGYzZjU0\",\"name\":\"electron\",\"displayName\":\"Electron\"},{\"id\":\"dGFnOmViZjcxZDEyODdjNjhkMTI1NTZhODBhMzBiMGQ3Zjg0\",\"name\":\"paddleocr\",\"displayName\":null},{\"id\":\"dGFnOjA5Mzc0Zjc4ZTA5MTgyZDA3NmQ1NjVkNTI2MTU0YWM1\",\"name\":\"search-photos\",\"displayName\":null},{\"id\":\"dGFnOjAwNTJlNjc3OGUyMWU3NDA2ZTA5NzNiMjA1MmUxNWI4\",\"name\":\"screen-recorder\",\"displayName\":null},{\"id\":\"dGFnOmUyMDZhNTRlOTc2OTBjY2U1MGNjODcyZGQ3MGVlODk2\",\"name\":\"linux\",\"displayName\":\"Linux\"},{\"id\":\"dGFnOjQzYjlkOGVhMThjNDhjM2E2NGM0ZTM3MzM4ZmM2Njhm\",\"name\":\"macos\",\"displayName\":\"macOS\"},{\"id\":\"dGFnOjBmNDEzN2VkMTUwMmI1MDQ1ZDYwODNhYTI1OGI1YzQy\",\"name\":\"windows\",\"displayName\":\"Windows\"},{\"id\":\"dGFnOjYwN2YyZjMwOTlmMmEzNDdiMzI3Y2FhNzBlMGJlNGIy\",\"name\":\"translator\",\"displayName\":null},{\"id\":\"dGFnOmZjNDZlMjZhOTA3ODcwNzQ0NzU4Yjc2MTY2MTUwZjYy\",\"name\":\"translate\",\"displayName\":null},{\"id\":\"dGFnOjk0ZjkwYzg0YzNkZWY1N2EzOTc1NDY3OWIwNGU4YjM2\",\"name\":\"recorder\",\"displayName\":null}],\"createAt\":\"2021-10-06T01:06:58Z\",\"updateAt\":\"2025-07-17T15:56:39Z\",\"user\":null,\"chinaFlag\":1,\"commentCount\":0,\"isArchived\":false,\"status\":1},{\"id\":\"cHJvamVjdDo2MzQxNTg0MDA4MTMxM2U3Njg0MzE0MjM=\",\"name\":\"LunaTranslator\",\"fullName\":\"HIllya51/LunaTranslator\",\"sourceUrl\":\"https://github.com/HIllya51/LunaTranslator\",\"starCount\":7568,\"forkCount\":895,\"description\":\"视觉小说翻译器，支持HOOK、OCR、剪贴板等。Visual Novel Translator, support HOOK / OCR / Clipboard\",\"remark\":null,\"lang\":\"C++\",\"screenshot\":null,\"url\":\"/projects/542386106-lunatranslator\",\"homepageUrl\":\"https://lunatranslator.org/\",\"category\":null,\"tags\":[{\"id\":\"dGFnOjJiMDRlZjdkNjA4NWVkZmNjNTVlZjIyZTEzYzBhYmVi\",\"name\":\"galgame\",\"displayName\":null},{\"id\":\"dGFnOjYwN2QzZTEzNzc4ODlkNDM3ZGRjMjYxMmFlM2Y2NjJi\",\"name\":\"visual-novel\",\"displayName\":\"Visual novel\"},{\"id\":\"dGFnOjYwN2YyZjMwOTlmMmEzNDdiMzI3Y2FhNzBlMGJlNGIy\",\"name\":\"translator\",\"displayName\":null},{\"id\":\"dGFnOjljNGY4OGY3MDZkZWRkZTNiYzBlYmI2NmUzNDk2M2U1\",\"name\":\"win32\",\"displayName\":null},{\"id\":\"dGFnOjUwYmJjNDAwODdiNGI5MjgwNWZhZDg5NDc3MTRkNDVk\",\"name\":\"reverse-engineering\",\"displayName\":\"逆向工程\"},{\"id\":\"dGFnOmU5MzdkN2ExNjE0NThmYjMxNGNjYmJjZTk0MTUwZTIy\",\"name\":\"ocr\",\"displayName\":\"OCR\"}],\"createAt\":\"2022-09-28T03:10:53Z\",\"updateAt\":\"2025-07-28T07:26:55Z\",\"user\":null,\"chinaFlag\":1,\"commentCount\":0,\"isArchived\":false,\"status\":0},{\"id\":\"cHJvamVjdDoxYWY3YzVkZTIyMDRhNGRiZDg2NmFmZDM4MzRkNTYxYg==\",\"name\":\"halo\",\"fullName\":\"halo-dev/halo\",\"sourceUrl\":\"https://github.com/halo-dev/halo\",\"starCount\":36137,\"forkCount\":9982,\"description\":\"强大易用的开源建站工具。\",\"remark\":null,\"lang\":\"Java\",\"screenshot\":\"https://repository-images.githubusercontent.com/126178683/adbb35f3-5a34-48a3-8183-1c1da0b3a3ac\",\"url\":\"/projects/126178683-halo\",\"homepageUrl\":\"https://www.halo.run\",\"category\":{\"name\":\"blog\",\"screenName\":\"博客\",\"id\":\"Y2F0ZWdvcnk6NWQyYWJhZGZiYTUyNGUwZjMwM2NiMmZi\"},\"tags\":[{\"id\":\"dGFnOjU3Zjg0MjI4NjE3MTA5NDg1NWU1MWZjM2E1NDFjMWUy\",\"name\":\"halo\",\"displayName\":null},{\"id\":\"dGFnOjU3MGQ5OWU0Yzg1OTE0NDcwZDkxNDE3MGQxZTk1MTQ0\",\"name\":\"cms\",\"displayName\":\"内容管理系统\"},{\"id\":\"dGFnOjE0MGQ5Zjc0MDQ3Yzk3MDc4ZGQzYjVkN2NjZjZiZjAy\",\"name\":\"halocms\",\"displayName\":null},{\"id\":\"dGFnOjA0NDRjYWQwZDk2ZjdiYTliZDhmYTczNWJjMmUyYWZj\",\"name\":\"content-management-system\",\"displayName\":\"内容管理系统\"},{\"id\":\"dGFnOjEyNmFjOWY2MTQ5MDgxZWIwZTk3YzJlOTM5ZWFhZDUy\",\"name\":\"blog\",\"displayName\":null},{\"id\":\"dGFnOjhjYTUyYWNkYjE5NjE5NGFhOTMwNGQ3NGQ4Y2Q0OWZi\",\"name\":\"blog-engine\",\"displayName\":null}],\"createAt\":\"2018-03-21T12:56:52Z\",\"updateAt\":\"2025-07-28T06:08:49Z\",\"user\":{\"name\":\"Halo\",\"remark\":null,\"poweredBy\":null,\"isVerified\":null,\"id\":\"48195280\"},\"chinaFlag\":1,\"commentCount\":0,\"isArchived\":false,\"status\":0},{\"id\":\"cHJvamVjdDozMGYwY2YwNzFlNDJiNzdlNjU1YzQ0OWEzMjA2ZDQ0OA==\",\"name\":\"new-pac\",\"fullName\":\"Alvin9999/new-pac\",\"sourceUrl\":\"https://github.com/Alvin9999/new-pac\",\"starCount\":66033,\"forkCount\":10189,\"description\":\"翻墙-科学上网、自由上网、免费科学上网、免费翻墙、fanqiang、油管youtube/视频下载、软件、VPN、一键翻墙浏览器，vps一键搭建翻墙服务器脚本/教程，免费shadowsocks/ss/ssr/v2ray/goflyway账号/节点，翻墙梯子，电脑、手机、iOS、安卓、windows、Mac、Linux、路由器翻墙、科学上网、youtube视频下载、youtube油管镜像/免翻墙网站、美区apple id共享账号、翻墙-科学上网-梯子\",\"remark\":null,\"lang\":\"Python\",\"screenshot\":null,\"url\":\"/projects/54544023-new-pac\",\"homepageUrl\":\"\",\"category\":null,\"tags\":[{\"id\":\"dGFnOjI3N2E4YTU1OWEwZmNjOWY0ZmE4NDgwZGViODY0ZWJj\",\"name\":\"fanqiang\",\"displayName\":null},{\"id\":\"dGFnOjI1MzhmOTdhYzA4ODc2ODAyZDM0ZDY3YWQzZmUzZmI2\",\"name\":\"free-ssr\",\"displayName\":null},{\"id\":\"dGFnOjdjZTZhMTdiYmIyMmNjZjc2MmZiMDgwNjI3OGIwOTYx\",\"name\":\"free-ss\",\"displayName\":null},{\"id\":\"dGFnOjk0ODZiMGMzN2FjN2Q2NzRmODY1YThkNGEzMjA5ZmU2\",\"name\":\"ssr\",\"displayName\":null},{\"id\":\"dGFnOmQyMTBiMDY4ODE1ZmQ4M2NjY2U2NWU1ZmU4MmY5MDVj\",\"name\":\"shadowsocks\",\"displayName\":\"Shadowsocks\"},{\"id\":\"dGFnOmI5ZmE5ODU0ZWM0ZDhkMDRlODIzMjE3YTJhNGY4ZjUy\",\"name\":\"v2ray\",\"displayName\":null},{\"id\":\"dGFnOjIxYzNmOTk2YmM2OWM0OTI2YTIxNmVmZDkwMDFjZjhk\",\"name\":\"shadowsocksr\",\"displayName\":null},{\"id\":\"dGFnOjM2OTEzMDhmMmE0YzJmNjk4M2YyODgwZDMyZTI5Yzg0\",\"name\":\"ss\",\"displayName\":\"Shadowsocks\"},{\"id\":\"dGFnOjYwMGMxMTc3YmZhOGE1NWE4YzE3OTExOWNlOWIwNWY3\",\"name\":\"vmess\",\"displayName\":null},{\"id\":\"dGFnOjQ4NDNkNjVjNWZmNWU4ZGMzNjRmNTA0OTc4MDlmNzQy\",\"name\":\"brook\",\"displayName\":null},{\"id\":\"dGFnOjk3YWE0ZWM5YTZlNTc2MjNiMzA2NjVhZGI4ZDVlMTFk\",\"name\":\"naiveproxy\",\"displayName\":null},{\"id\":\"dGFnOjI4MDY5MDZjNzljZGI4ZWYwNWQ5NTk1ZmY4Y2RjMTI1\",\"name\":\"trojan\",\"displayName\":null},{\"id\":\"dGFnOjAxZmFmMzgzNjUxNTFmOGQ5NjZiZjU5NjAyNDJmYjll\",\"name\":\"vpn\",\"displayName\":\"Virtual Private Network\"},{\"id\":\"dGFnOmJhOWJmMDU2OTNiOWZhMjAyZDkyMmRkNDNhMDhmMjgx\",\"name\":\"youtube\",\"displayName\":\"YouTube\"},{\"id\":\"dGFnOmQ3ZjYxZmU5NjYwODRkZjQ3M2IwMTFkNjczMTIxYmRk\",\"name\":\"hysteria\",\"displayName\":null},{\"id\":\"dGFnOjMyNTk5ZTUzODcyNTFhNDc3ZTE2ODk0ZGNhNzQxN2Jm\",\"name\":\"xray\",\"displayName\":null},{\"id\":\"dGFnOmZmZjY0MGViYjQ0MGQzY2I1NDk0MjIzYjVjNjMxNjAx\",\"name\":\"sing-box\",\"displayName\":null},{\"id\":\"************************************************\",\"name\":\"vless\",\"displayName\":null},{\"id\":\"dGFnOmJiODlkYmY3ZDc1NDBlZWIyNzBkOTFhYmZkNTgwMDA4\",\"name\":\"clash\",\"displayName\":\"Clash\"}],\"createAt\":\"2016-03-23T08:43:36Z\",\"updateAt\":\"2025-07-28T07:52:32Z\",\"user\":{\"name\":\"自由上网\",\"remark\":null,\"poweredBy\":null,\"isVerified\":null,\"id\":\"12132898\"},\"chinaFlag\":1,\"commentCount\":0,\"isArchived\":false,\"status\":1},{\"id\":\"cHJvamVjdDo2MDhhOTFiZGY1YzhjOWE0YzMxMjE2Nzg=\",\"name\":\"CS-Books\",\"fullName\":\"forthespada/CS-Books\",\"sourceUrl\":\"https://github.com/forthespada/CS-Books\",\"starCount\":24182,\"forkCount\":3931,\"description\":\"🔥🔥超过1000本的计算机经典书籍、个人笔记资料以及本人在各平台发表文章中所涉及的资源等。书籍资源包括C/C++、Java、Python、Go语言、数据结构与算法、操作系统、后端架构、计算机系统知识、数据库、计算机网络、设计模式、前端、汇编以及校招社招各种面经~\",\"remark\":null,\"lang\":null,\"screenshot\":null,\"url\":\"/projects/328349603-cs-books\",\"homepageUrl\":\"https://interviewguide.cn\",\"category\":{\"name\":\"interview\",\"screenName\":\"面试\",\"id\":\"Y2F0ZWdvcnk6NWQyYWJiNmViYTUyNGUwZjMwM2NiMmZj\"},\"tags\":[{\"id\":\"dGFnOmI3ZmY3OTQxMjNlOTI5Njg3MmIwMWI4M2JkNTQwZTAy\",\"name\":\"cs-books\",\"displayName\":null},{\"id\":\"dGFnOjRhOGEwOGYwOWQzN2I3Mzc5NTY0OTAzODQwOGI1ZjMz\",\"name\":\"c\",\"displayName\":\"C\"},{\"id\":\"dGFnOjJjNWYyY2JhOWFmN2Y5Mjk4NmQzZGUxZTc1M2YzZmFh\",\"name\":\"cpp\",\"displayName\":\"C++\"},{\"id\":\"dGFnOjIzZWVlYjQzNDdiZGQyNmJmYzZiN2VlOWEzYjc1NWRk\",\"name\":\"python\",\"displayName\":\"Python\"},{\"id\":\"dGFnOmRlOWI5ZWQ3OGQ3ZTJlMWRjZWVmZmVlNzgwZTJmOTE5\",\"name\":\"javascript\",\"displayName\":\"JavaScript\"},{\"id\":\"dGFnOjY2MjcwNzA3NDI0YTcyOWMzZTU1N2ZjZWIwM2Y0NWM5\",\"name\":\"algorithms\",\"displayName\":\"算法\"},{\"id\":\"dGFnOmUyMDZhNTRlOTc2OTBjY2U1MGNjODcyZGQ3MGVlODk2\",\"name\":\"linux\",\"displayName\":\"Linux\"},{\"id\":\"dGFnOmRkMzAyZjk0NjgyZGJkMmExMTRkNjNiMDQzMzYwMmUw\",\"name\":\"os\",\"displayName\":\"操作系统\"},{\"id\":\"dGFnOjExZTBlZWQ4ZDM2OTZjMGE2MzJmODIyZGYzODVhYjNj\",\"name\":\"database\",\"displayName\":\"数据库\"},{\"id\":\"dGFnOmFjNWM3NGI2NGI0YjgzNTJlZjJmMTgxYWZmYjVhYzJh\",\"name\":\"sql\",\"displayName\":\"SQL\"},{\"id\":\"dGFnOjg2YTFiOTA3ZDU0YmY3MDEwMzk0YmYzMTZlMTgzZTY3\",\"name\":\"redis\",\"displayName\":\"Redis\"},{\"id\":\"dGFnOjkzZjcyNWEwNzQyM2ZlMWM4ODlmNDQ4YjMzZDIxZjQ2\",\"name\":\"java\",\"displayName\":\"Java\"},{\"id\":\"dGFnOjA0OTgwNzQ0Zjc0ZjRlYzM2YWQ1YTlkNWZlYzg4NzZm\",\"name\":\"interview\",\"displayName\":\"面试\"},{\"id\":\"dGFnOjQzNzE3NWJhNDE5MTIxMGVlMDA0ZTFkOTM3NDk0ZDA5\",\"name\":\"pdf\",\"displayName\":null}],\"createAt\":\"2021-01-10T09:49:05Z\",\"updateAt\":\"2025-07-10T06:08:14Z\",\"user\":null,\"chinaFlag\":1,\"commentCount\":0,\"isArchived\":false,\"status\":0},{\"id\":\"cHJvamVjdDo2NDQ5ZWY0OTYyMzYyNDRmZmFhMTIzZDc=\",\"name\":\"FactoryBluePrints\",\"fullName\":\"DSPBluePrints/FactoryBluePrints\",\"sourceUrl\":\"https://github.com/DSPBluePrints/FactoryBluePrints\",\"starCount\":1657,\"forkCount\":304,\"description\":\"游戏戴森球计划的**工厂**蓝图仓库\",\"remark\":null,\"lang\":\"Text\",\"screenshot\":null,\"url\":\"/projects/523449876-factoryblueprints\",\"homepageUrl\":\"\",\"category\":null,\"tags\":[],\"createAt\":\"2022-08-10T18:12:11Z\",\"updateAt\":\"2025-07-20T13:51:09Z\",\"user\":null,\"chinaFlag\":1,\"commentCount\":0,\"isArchived\":false,\"status\":0},{\"id\":\"cHJvamVjdDo2MzM2Y2MzOWU2ZGUyMWQyNzE5ZjAwOGE=\",\"name\":\"reference\",\"fullName\":\"jaywcjlove/reference\",\"sourceUrl\":\"https://github.com/jaywcjlove/reference\",\"starCount\":14265,\"forkCount\":2123,\"description\":\"为开发人员分享快速参考备忘清单(速查表)\",\"remark\":null,\"lang\":\"Dockerfile\",\"screenshot\":\"https://repository-images.githubusercontent.com/540917299/1e19ea9e-3d05-49c8-897a-9d2fa2bc90d5\",\"url\":\"/projects/540917299-reference\",\"homepageUrl\":\"https://jaywcjlove.github.io/reference\",\"category\":null,\"tags\":[{\"id\":\"dGFnOmRlOWI5ZWQ3OGQ3ZTJlMWRjZWVmZmVlNzgwZTJmOTE5\",\"name\":\"javascript\",\"displayName\":\"JavaScript\"},{\"id\":\"dGFnOjA1YjYwNTNjNDFhMjEzMGFmZDZmYzNiMTU4YmRhNGU2\",\"name\":\"docker\",\"displayName\":\"Docker\"},{\"id\":\"dGFnOmJiMzBlODU0MTFiNTZkZjQxMjk2NzI2YWI0NDVkYzhm\",\"name\":\"npm\",\"displayName\":\"npm\"},{\"id\":\"dGFnOjA0YjAyYjJkYmM1NGU0NWU3NzBkMjZiYTVkOWJkMDlk\",\"name\":\"npm-package\",\"displayName\":\"npm\"},{\"id\":\"dGFnOjliNGExYjk1N2M2NzIyMGQ5MDAwZDkyNzM0Mjc5YjRj\",\"name\":\"semver\",\"displayName\":null},{\"id\":\"dGFnOmVjMThhZDlhODNjMzEyMzgwNTMzZmE5M2FjNmZjNjFj\",\"name\":\"toml\",\"displayName\":null},{\"id\":\"dGFnOjFlMzViY2JhMmRiYTEwODljN2JkMDgwNWQ0NTE3Yzhm\",\"name\":\"typescript\",\"displayName\":\"TypeScript\"},{\"id\":\"dGFnOmU1ZmZjMDQyZjAwYjEyNTJhM2NjYmJlYzUzYTcwMzc2\",\"name\":\"cheatsheet\",\"displayName\":null},{\"id\":\"dGFnOjQ0YTJkYzkxZTFlMjFiNjUzZmQ3ZmY2YzYzZWNhNTU1\",\"name\":\"references\",\"displayName\":null},{\"id\":\"dGFnOjNjNzk3MjcwMTAyNDgwY2I0ZDEyMDdkMWUxZDA3YTMz\",\"name\":\"reactjs\",\"displayName\":\"React\"},{\"id\":\"dGFnOjI2NmExZjdjMmUyMzQ1MTY5ZDNiYzQ0OGRhNDVlYWU2\",\"name\":\"react\",\"displayName\":\"React\"},{\"id\":\"dGFnOmNiOWY4MTg3ZDIzNzMyYTkxZDliNTM4MDQ0MWM2MThl\",\"name\":\"vue3\",\"displayName\":\"Vue.js\"},{\"id\":\"dGFnOmQxNGJiMDNmNmM5ZTBiZTE0MWFiNzMyYzFmZDQ3YjBh\",\"name\":\"vuejs\",\"displayName\":\"Vue.js\"}],\"createAt\":\"2022-09-24T17:49:19Z\",\"updateAt\":\"2025-07-26T17:57:12Z\",\"user\":{\"name\":\"小弟调调\",\"remark\":null,\"poweredBy\":null,\"isVerified\":null,\"id\":\"1680273\"},\"chinaFlag\":1,\"commentCount\":0,\"isArchived\":false,\"status\":0},{\"id\":\"cHJvamVjdDo2MWIzYTNiZGRmZjg5NzkzMjk3MDg3NzQ=\",\"name\":\"RuoYi-Vue3\",\"fullName\":\"yangzongzhuan/RuoYi-Vue3\",\"sourceUrl\":\"https://github.com/yangzongzhuan/RuoYi-Vue3\",\"starCount\":5855,\"forkCount\":2274,\"description\":\":tada: (RuoYi)官方仓库 基于SpringBoot，Spring Security，JWT，Vue3 \u0026 Vite、Element Plus 的前后端分离权限管理系统\",\"remark\":null,\"lang\":\"Vue\",\"screenshot\":null,\"url\":\"/projects/433037416-ruoyi-vue3\",\"homepageUrl\":\"http://ruoyi.vip\",\"category\":null,\"tags\":[{\"id\":\"dGFnOmNiOWY4MTg3ZDIzNzMyYTkxZDliNTM4MDQ0MWM2MThl\",\"name\":\"vue3\",\"displayName\":\"Vue.js\"},{\"id\":\"dGFnOmU5YmM5YjA5M2M2YTM4YzkwMDIxYWQ1NjdjYTcxMzNh\",\"name\":\"vite2\",\"displayName\":null},{\"id\":\"dGFnOmQxODg4M2I4YjBjZjk3MDcxYjg0OGY3ZGRkNWU4Yzdk\",\"name\":\"element-plus\",\"displayName\":null},{\"id\":\"dGFnOmE1MTM2NjI4NjVkOWQ5OGJhNzA0ZDM0YzBkNTIyYzRm\",\"name\":\"vue-router\",\"displayName\":null},{\"id\":\"dGFnOjk3YTIwZDVjNDgzMTI2MTJhNTc1NjE4ODYwZjNhMDVh\",\"name\":\"element-ui\",\"displayName\":null},{\"id\":\"************************************************\",\"name\":\"vue\",\"displayName\":\"Vue.js\"},{\"id\":\"dGFnOjRkYTA2NmRhYWY2MzQ3MTJlNmM0OGE1ZWI3MmNkOWMz\",\"name\":\"vite\",\"displayName\":\"Vite\"},{\"id\":\"dGFnOjIxMjMyZjI5N2E1N2E1YTc0Mzg5NGEwZTRhODAxZmMz\",\"name\":\"admin\",\"displayName\":null},{\"id\":\"dGFnOjFmYmM3MzE0OGE3MzA2ZDhjM2IyZTg3ZTUyYzU2MWE3\",\"name\":\"vue-admin\",\"displayName\":null},{\"id\":\"dGFnOjIyMjBmYzg5OTUyN2ZjOTYxOTFmZjI3YjczMmNkZjZm\",\"name\":\"vue-element-admin\",\"displayName\":null},{\"id\":\"dGFnOjk5MjdhYTk1M2Y4YzAwMDcwYTk1MDU2YzNjOTllNzgx\",\"name\":\"vue3-admin\",\"displayName\":null}],\"createAt\":\"2021-11-29T12:39:57Z\",\"updateAt\":\"2025-07-14T04:52:30Z\",\"user\":null,\"chinaFlag\":1,\"commentCount\":0,\"isArchived\":false,\"status\":0}]},\"dailyTrends\":{\"dateRange\":\"daily\",\"issueId\":25072807,\"updateAt\":\"28 Jul 2025 07:00:19 GMT\",\"projects\":[{\"id\":\"cHJvamVjdDpjSEp2YW1WamREbzJOVFZoWXpVMU5qTTBPVGt6WVRFMVpXVXlZV05qWVdZPQ==\",\"name\":\"Genesis\",\"fullName\":\"Genesis-Embodied-AI/Genesis\",\"sourceUrl\":\"https://github.com/Genesis-Embodied-AI/Genesis\",\"starCount\":26443,\"forkCount\":2410,\"description\":\"A generative world for general-purpose robotics \u0026 embodied AI learning.\",\"remark\":\"Genesis 是专为机器人/嵌入式 AI/物理 AI 应用设计的通用物理平台\",\"lang\":\"Python\",\"screenshot\":null,\"url\":\"/projects/712225112-genesis\",\"homepageUrl\":\"https://genesis-world.readthedocs.io\",\"category\":null,\"tags\":[],\"createAt\":\"2023-10-31T03:33:11Z\",\"updateAt\":\"2025-07-26T20:19:48Z\",\"user\":null,\"chinaFlag\":0,\"commentCount\":0,\"isArchived\":false,\"status\":0},{\"id\":\"cHJvamVjdDpjSEp2YW1WamREbzJOak0yTVRVellXTmlaamxoTVROalpHTTFOVEE1WVRBPQ==\",\"name\":\"awesome-llm-apps\",\"fullName\":\"Shubhamsaboo/awesome-llm-apps\",\"sourceUrl\":\"https://github.com/Shubhamsaboo/awesome-llm-apps\",\"starCount\":52508,\"forkCount\":6134,\"description\":\"Collection of awesome LLM apps with AI Agents and RAG using OpenAI, Anthropic, Gemini and opensource models.\",\"remark\":\"精选收录采用RAG、AI智能体、MCP、语音智能体等技术构建的优质LLM应用。\",\"lang\":\"Python\",\"screenshot\":null,\"url\":\"/projects/793375104-awesome-llm-apps\",\"homepageUrl\":\"https://www.theunwindai.com\",\"category\":null,\"tags\":[{\"id\":\"dGFnOmE1NDc1MGNhOTMwMjdmNWIyYzY1MjM5OTI1OTQ1OWFi\",\"name\":\"llms\",\"displayName\":\"大语言模型\"},{\"id\":\"dGFnOjc1NDJkYzdlOTEyMTYyZDdlNzk4MTE3NmNjYjQxYmJk\",\"name\":\"rag\",\"displayName\":null},{\"id\":\"dGFnOjIzZWVlYjQzNDdiZGQyNmJmYzZiN2VlOWEzYjc1NWRk\",\"name\":\"python\",\"displayName\":\"Python\"}],\"createAt\":\"2024-04-29T05:30:25Z\",\"updateAt\":\"2025-07-26T15:36:31Z\",\"user\":null,\"chinaFlag\":0,\"commentCount\":0,\"isArchived\":false,\"status\":0},{\"id\":\"cHJvamVjdDpjSEp2YW1WamREbzJORGN4T1RJeE1XWTNPVEZrWmpZd1lURmxOalV5TVRNPQ==\",\"name\":\"hrms\",\"fullName\":\"frappe/hrms\",\"sourceUrl\":\"https://github.com/frappe/hrms\",\"starCount\":4984,\"forkCount\":1415,\"description\":\"Open Source HR and Payroll Software\",\"remark\":\"Frappe HR 是一款开源、现代化的人力资源与薪资管理软件\",\"lang\":\"Python\",\"screenshot\":\"https://repository-images.githubusercontent.com/501292795/010f358b-8afb-4e41-a7c8-8444983055c8\",\"url\":\"/projects/501292795-hrms\",\"homepageUrl\":\"https://frappe.io/hr\",\"category\":null,\"tags\":[{\"id\":\"dGFnOmFkYWI3YjcwMWYyM2JiODIwMTRjODUwNmQzZGM3ODRl\",\"name\":\"hr\",\"displayName\":null},{\"id\":\"dGFnOjEwNTc1NGU2MzRjMzJjMDNhYTdmZGNlMDU3M2MzMzQz\",\"name\":\"hrms\",\"displayName\":null},{\"id\":\"dGFnOjk2ZDAyMDVhMDAxMDU2ZGVhMDJmMDZiMTE1MzNmNGFh\",\"name\":\"payroll\",\"displayName\":null},{\"id\":\"dGFnOjBlMDFkMTE0YjVhZDg4NjI4ZWFjZWI0NzI4MjQyODRh\",\"name\":\"erpnext\",\"displayName\":null},{\"id\":\"dGFnOmNkYzg3NzNiMGYxNTQxZWNmZGJiNTQ2NWE2NmYxODRk\",\"name\":\"hcm\",\"displayName\":null},{\"id\":\"dGFnOmMwMmY0MDliZDFmMmFkMGU1N2ZkMzAxOTU4YWVkNDcw\",\"name\":\"hris\",\"displayName\":null},{\"id\":\"dGFnOmZhNTQ3MzUzMGU0ZDFhNWExZTFlYjUzZDJmZWRiMTBj\",\"name\":\"employee\",\"displayName\":null},{\"id\":\"dGFnOjI3Njg5YjQwZGM0ODY2NGEzZjAyNjJiZDlhZTg3NzVh\",\"name\":\"performance-management\",\"displayName\":null},{\"id\":\"dGFnOjA1MzZkZmNiOWJiYTY5N2M2YmM0NWMwMmViMjA0OGFl\",\"name\":\"recruitment\",\"displayName\":null},{\"id\":\"dGFnOjY0YzYyYzkyYWMwNjQ1MDBkNjBhN2YzNjI0ZTM1YWQ3\",\"name\":\"attendance\",\"displayName\":null},{\"id\":\"dGFnOmYxZWQwNTg2MGFjZjU4YTgyMzI1Y2NlOTc0M2MxZjc0\",\"name\":\"leave-management\",\"displayName\":null},{\"id\":\"dGFnOjFkYmExZTdmMGFmNTAzOTQ5NWEwYjY5Zjc1NmY0ZmJi\",\"name\":\"shift-management\",\"displayName\":null},{\"id\":\"dGFnOjc1ODE3N2E2NjA0NTI3ZDE4NjgyMDVkN2VhYWI1NWFi\",\"name\":\"exits\",\"displayName\":null},{\"id\":\"dGFnOmQ2M2Q2ZDFmNjY5YTczOTE3OWU0NWM2Yjg5ODIxZWYw\",\"name\":\"frappe\",\"displayName\":null},{\"id\":\"dGFnOjQwMzE2Nzc2ZjJkZmQ2ZGYzYzJjNjhjODljN2YxZWM3\",\"name\":\"frappe-framework\",\"displayName\":null},{\"id\":\"dGFnOmRlOWI5ZWQ3OGQ3ZTJlMWRjZWVmZmVlNzgwZTJmOTE5\",\"name\":\"javascript\",\"displayName\":\"JavaScript\"},{\"id\":\"dGFnOjVkYWM5NGNhNjYzNTdlY2QzODBmOTEwNTBiMDE0NDll\",\"name\":\"onboarding\",\"displayName\":null},{\"id\":\"dGFnOjU4OTYxMmY4NmRiMmEyYjQ4M2IwMDdiYzJhMWU5NjY1\",\"name\":\"open-source\",\"displayName\":\"Open Source\"},{\"id\":\"dGFnOjIzZWVlYjQzNDdiZGQyNmJmYzZiN2VlOWEzYjc1NWRk\",\"name\":\"python\",\"displayName\":\"Python\"},{\"id\":\"dGFnOjA3YmE4YTkyNDdhMGY5MWUwMjVkODAzNmEyOGMzYjU2\",\"name\":\"pwa\",\"displayName\":\"PWA\"}],\"createAt\":\"2022-06-08T14:45:49Z\",\"updateAt\":\"2025-07-28T05:38:29Z\",\"user\":{\"name\":\"Frappe\",\"remark\":null,\"poweredBy\":null,\"isVerified\":null,\"id\":\"836974\"},\"chinaFlag\":0,\"commentCount\":0,\"isArchived\":false,\"status\":0},{\"id\":\"cHJvamVjdDpjSEp2YW1WamREbzJNMk16WkdOaU9UazJOV1ppTlRRM1lUQmhaV1JrT1dZPQ==\",\"name\":\"hyperswitch\",\"fullName\":\"juspay/hyperswitch\",\"sourceUrl\":\"https://github.com/juspay/hyperswitch\",\"starCount\":23048,\"forkCount\":3912,\"description\":\"An open source payments switch written in Rust to make payments fast, reliable and affordable\",\"remark\":\"hyperswitch 是一种用Rust编写的支付切换代理，可让您通过单个API 集成连接多个支付处理系统\",\"lang\":\"Rust\",\"screenshot\":\"https://repository-images.githubusercontent.com/552877440/fd8b83bc-a093-4f7b-9e16-a0cd1f9f8572\",\"url\":\"/projects/552877440-hyperswitch\",\"homepageUrl\":\"https://hyperswitch.io/\",\"category\":null,\"tags\":[{\"id\":\"dGFnOjg0ZDVlYWY3MTNjOTZlZWNiM2QyYzRhODNlNjRkYzlh\",\"name\":\"payments\",\"displayName\":null},{\"id\":\"dGFnOjcyODEyZTMwODczNDU1ZGNlZTJjZTJkMWVlMjZlNGFi\",\"name\":\"rust\",\"displayName\":\"Rust\"},{\"id\":\"dGFnOmMwMTQ2ZWRlODMwMTFlNGY0ZjcxYjlmNzRmY2Y5M2Iz\",\"name\":\"orchestration\",\"displayName\":null},{\"id\":\"dGFnOjJiMzE3Y2QyZTVhNWQ3YmJlNzQ3MWI0MTljMjNlZWMw\",\"name\":\"hacktoberfest\",\"displayName\":\"Hacktoberfest\"},{\"id\":\"dGFnOjRmYzQ0ODQ2Y2Y3ZWQ4M2U2YjY5NDc5MzZlZjM2Yjc2\",\"name\":\"beginner-friendly\",\"displayName\":null},{\"id\":\"dGFnOmRkMGQyNGE0ODE5N2IyNzg1ZTM0ZmE2NzRkNWRhMmUz\",\"name\":\"featured\",\"displayName\":null},{\"id\":\"dGFnOmJlMjNjZTkzYmI0ZjQ5MTA3ZDFmYzljMTczNjU0ZWQy\",\"name\":\"high-performance\",\"displayName\":null},{\"id\":\"dGFnOjU4OTYxMmY4NmRiMmEyYjQ4M2IwMDdiYzJhMWU5NjY1\",\"name\":\"open-source\",\"displayName\":\"Open Source\"},{\"id\":\"dGFnOmEzMTIzM2M5NWQ0Mzc3Y2Q4Y2E2OWFlMzc2NDMxZjc4\",\"name\":\"restful-api\",\"displayName\":null},{\"id\":\"dGFnOmVhZTE4YmM0MWUxNDM0ZGQ5OGZhMmRkOTg5NTMxZGE4\",\"name\":\"sdk\",\"displayName\":\"SDK\"},{\"id\":\"dGFnOmE2MzA4OWVlNzJiOWNjNWZiNzMzZWE5ODgyM2YxY2Jm\",\"name\":\"works-with-react\",\"displayName\":null},{\"id\":\"dGFnOmU0NzI4ZjQ0NGIyNDgzOWUzZjgwYWRmMzgyOWJjYmE5\",\"name\":\"postgresql\",\"displayName\":\"PostgreSQL\"},{\"id\":\"dGFnOjg2YTFiOTA3ZDU0YmY3MDEwMzk0YmYzMTZlMTgzZTY3\",\"name\":\"redis\",\"displayName\":\"Redis\"},{\"id\":\"dGFnOjU3MzM2YWZkMWY0YjQwZGZkOWY1NzMxZTM1MzAyZmU1\",\"name\":\"finance\",\"displayName\":null}],\"createAt\":\"2022-10-17T11:18:28Z\",\"updateAt\":\"2025-07-28T06:15:24Z\",\"user\":null,\"chinaFlag\":0,\"commentCount\":0,\"isArchived\":false,\"status\":0},{\"id\":\"cHJvamVjdDpjSEp2YW1WamREbzJOMkUzTkRaaE1XWmhOMk5sWkRnM09HSTVNbU0wWXpjPQ==\",\"name\":\"ai-cookbook\",\"fullName\":\"daveebbelaar/ai-cookbook\",\"sourceUrl\":\"https://github.com/daveebbelaar/ai-cookbook\",\"starCount\":1916,\"forkCount\":801,\"description\":\"Examples and tutorials to help developers build AI systems\",\"remark\":null,\"lang\":\"Python\",\"screenshot\":null,\"url\":\"/projects/924738875-ai-cookbook\",\"homepageUrl\":\"https://www.youtube.com/@daveebbelaar\",\"category\":{\"name\":\"llm\",\"screenName\":\"大语言模型\",\"id\":\"Y2F0ZWdvcnk6NjcyMjVhNzU3MmYxNjIzNWFjMDY3NTc5\"},\"tags\":[{\"id\":\"dGFnOjc2ZWU0YWQzYjEyYmEzNDcxYjUyNzNkMjZmNDhiNTFi\",\"name\":\"agents\",\"displayName\":null},{\"id\":\"dGFnOjQ5MjFjMGUyZDFmNjAwNWFiZTFmOWVjMmUyMDQxOTA5\",\"name\":\"ai\",\"displayName\":\"人工智能\"},{\"id\":\"dGFnOjU1NmRjZDI2YTU0ZmI3NTIzYjQ2MmY2MGNkY2Q0NGUx\",\"name\":\"anthropic\",\"displayName\":null},{\"id\":\"dGFnOmRhZTFiZTg1ZGFiMzY1MGViNTZiODdjNGUzMzkwMzg3\",\"name\":\"llm\",\"displayName\":\"大语言模型\"},{\"id\":\"dGFnOjJjNmVlMjRiMDk4MTZhNmYxNGY5NWQxNjk4YjI0ZWFk\",\"name\":\"openai\",\"displayName\":null},{\"id\":\"dGFnOjIzZWVlYjQzNDdiZGQyNmJmYzZiN2VlOWEzYjc1NWRk\",\"name\":\"python\",\"displayName\":\"Python\"}],\"createAt\":\"2025-01-30T15:14:29Z\",\"updateAt\":\"2025-07-25T10:17:34Z\",\"user\":null,\"chinaFlag\":0,\"commentCount\":0,\"isArchived\":false,\"status\":0},{\"id\":\"cHJvamVjdDpjSEp2YW1WamREcG1ZV1V5TWpRME9HRmpPRFl3T0RGaE1EYzVabUV6TkdJeE5qazJPR0UzWXc9PQ==\",\"name\":\"tldr\",\"fullName\":\"tldr-pages/tldr\",\"sourceUrl\":\"https://github.com/tldr-pages/tldr\",\"starCount\":56930,\"forkCount\":4590,\"description\":\"📚 Collaborative cheatsheets for console commands\",\"remark\":\"tldr 是一个命令用法帮助查询工具，比传统的 man 命令更好用。\",\"lang\":\"Markdown\",\"screenshot\":\"https://repository-images.githubusercontent.com/15019962/aa6a8d00-b4a3-11ea-92f4-5cca1da75be2\",\"url\":\"/projects/15019962-tldr\",\"homepageUrl\":\"https://tldr.sh\",\"category\":{\"name\":\"productivity-tools\",\"screenName\":\"效率工具集合\",\"id\":\"Y2F0ZWdvcnk6NjcyYTJhZTJkYjhlOGM2NDNlYTYwODY3\"},\"tags\":[{\"id\":\"dGFnOjI1OTFjOThiNzAxMTlmZTYyNDg5OGIxZTQyNGI1ZTkx\",\"name\":\"shell\",\"displayName\":\"Shell\"},{\"id\":\"dGFnOjNkZmVkZTczNWQxMjFiYzg1M2ViMmVmZmMwYjQwNWY2\",\"name\":\"man-page\",\"displayName\":null},{\"id\":\"dGFnOmEwMjdmOWVmODE3ZjVkODk4YzRhZTYwYzk3YzE0ZDI3\",\"name\":\"tldr\",\"displayName\":\"tldr pages\"},{\"id\":\"dGFnOmVhNGI0MGZhNzMwYmVkODc4MmFjNjMzMWM0ZTQ0OTVm\",\"name\":\"manpages\",\"displayName\":null},{\"id\":\"dGFnOjU1ODc2MjI4ODUzYWJmNjMyZGVjOTM0NmE0ZjM3MmVj\",\"name\":\"documentation\",\"displayName\":\"文档\"},{\"id\":\"dGFnOmVkZTk5N2IwY2FmMmVjMzk4MTEwZDc5ZDllYmEzOGJi\",\"name\":\"terminal\",\"displayName\":\"终端\"},{\"id\":\"dGFnOjAzZjMwZTllZDFhZDMwOGZjYmIwMjA4YWYwMGFmNmRh\",\"name\":\"command-line\",\"displayName\":\"命令行界面\"},{\"id\":\"dGFnOmJmYWZkODEzZDdlYTY1ZWU0ZGIxZjA5ZDdjOGZmYmY0\",\"name\":\"console\",\"displayName\":null},{\"id\":\"dGFnOmJmZWJlMzQxNTRhMGRmZDlmYzdiNDQ3ZmM5ZWQ3NGU5\",\"name\":\"examples\",\"displayName\":\"Example\"},{\"id\":\"dGFnOjY1N2Y4YjhkYTYyOGVmODNjZjY5MTAxYjY4MTcxNTBh\",\"name\":\"help\",\"displayName\":null},{\"id\":\"dGFnOjNjNzhiMzU1MDJiMjY5M2ZlZmRmYzUxY2JhM2E1M2E1\",\"name\":\"manual\",\"displayName\":null},{\"id\":\"dGFnOjJiMzE3Y2QyZTVhNWQ3YmJlNzQ3MWI0MTljMjNlZWMw\",\"name\":\"hacktoberfest\",\"displayName\":\"Hacktoberfest\"},{\"id\":\"dGFnOmU1ZmZjMDQyZjAwYjEyNTJhM2NjYmJlYzUzYTcwMzc2\",\"name\":\"cheatsheet\",\"displayName\":null},{\"id\":\"dGFnOjM4M2U2MGNiNWJjODA5MTFmY2IxYjRkMGNjMWVhM2Uw\",\"name\":\"cheatsheets\",\"displayName\":null},{\"id\":\"dGFnOmMzMWIzMjM2NGNlMTljYThmY2QxNTBhNDE3ZWNjZTU4\",\"name\":\"android\",\"displayName\":\"Android\"},{\"id\":\"dGFnOjc1OWI1MWVkZGI4OWExM2MxOWI0MWNhZTVjNTY1NjQ4\",\"name\":\"bsd\",\"displayName\":null},{\"id\":\"dGFnOmUyMDZhNTRlOTc2OTBjY2U1MGNjODcyZGQ3MGVlODk2\",\"name\":\"linux\",\"displayName\":\"Linux\"},{\"id\":\"dGFnOjQzYjlkOGVhMThjNDhjM2E2NGM0ZTM3MzM4ZmM2Njhm\",\"name\":\"macos\",\"displayName\":\"macOS\"},{\"id\":\"dGFnOjhlNGY4OGU5ZDZlYzAxMDZjY2M3M2IzOWQzNjljM2U4\",\"name\":\"osx\",\"displayName\":null},{\"id\":\"dGFnOjBmNDEzN2VkMTUwMmI1MDQ1ZDYwODNhYTI1OGI1YzQy\",\"name\":\"windows\",\"displayName\":\"Windows\"}],\"createAt\":\"2013-12-08T07:34:43Z\",\"updateAt\":\"2025-07-28T06:37:05Z\",\"user\":{\"name\":\"tldr pages\",\"remark\":null,\"poweredBy\":null,\"isVerified\":null,\"id\":\"7366472\"},\"chinaFlag\":-1,\"commentCount\":0,\"isArchived\":false,\"status\":0},{\"id\":\"cHJvamVjdDpjSEp2YW1WamREbzJNemRtTkdWaVpXWXlObVJoTlRaaU9EQTFPRFk1WW1FPQ==\",\"name\":\"infisical\",\"fullName\":\"Infisical/infisical\",\"sourceUrl\":\"https://github.com/Infisical/infisical\",\"starCount\":19474,\"forkCount\":1285,\"description\":\"Infisical is the open-source platform for secrets management, PKI, and SSH access.\",\"remark\":\"Infisical 是一个端到端加密的密钥管理平台，主要用于集中管理应用程序的配置密钥信息，例如API 密钥、数据库凭据和环境变量。它旨在简化开发人员的工作流程，并提高应用程序的安全性\",\"lang\":\"TypeScript\",\"screenshot\":\"https://repository-images.githubusercontent.com/521655652/f4dfe248-8d79-4dc0-a1c7-a368961856f5\",\"url\":\"/projects/521655652-infisical\",\"homepageUrl\":\"https://infisical.com\",\"category\":null,\"tags\":[{\"id\":\"dGFnOjA4OThiMjI3MzBkNTdhZmNkMzk0ZDhlNDg4OWVjZTRh\",\"name\":\"cli\",\"displayName\":\"命令行界面\"},{\"id\":\"dGFnOjVhM2M1MjE0NDJmMTBiYzQ3OTJjNDlmYTM3NDQ0MTg0\",\"name\":\"environment-variables\",\"displayName\":null},{\"id\":\"dGFnOmUxOGE0ODM3NGViZTFhZTI3YWQ3NmEzMzU5ZTc2Yjg0\",\"name\":\"secret-management\",\"displayName\":null},{\"id\":\"dGFnOjdkZTM4ZjNjM2QzYmFhN2NhNThhMzY2ZjA5NTc3NTg2\",\"name\":\"secrets\",\"displayName\":null},{\"id\":\"dGFnOmU5MWU2MzQ4MTU3ODY4ZGU5ZGQ4YjI1YzgxYWViZmI5\",\"name\":\"security\",\"displayName\":\"安全\"},{\"id\":\"dGFnOjU4OTYxMmY4NmRiMmEyYjQ4M2IwMDdiYzJhMWU5NjY1\",\"name\":\"open-source\",\"displayName\":\"Open Source\"},{\"id\":\"dGFnOjIxY2MyODQwOTcyOTU2NWZjMWE0ZDJkZDkyZGIyNjlm\",\"name\":\"golang\",\"displayName\":\"Go\"},{\"id\":\"dGFnOjFlMzViY2JhMmRiYTEwODljN2JkMDgwNWQ0NTE3Yzhm\",\"name\":\"typescript\",\"displayName\":\"TypeScript\"},{\"id\":\"dGFnOjY4YjM0MWI3ZTljYmFmOWU3NGExNjc0MjNjYmEwZTgy\",\"name\":\"secret-manager\",\"displayName\":null},{\"id\":\"dGFnOjM0ZDFmOTFmYjJlNTE0Yjg1NzZmYWIxYTc1YTg5YTZi\",\"name\":\"go\",\"displayName\":\"Go\"},{\"id\":\"dGFnOmU4YTQ4NjUzODUxZTI4YzY5ZDA1MDY1MDhmYjI3ZmM1\",\"name\":\"postgres\",\"displayName\":\"PostgreSQL\"},{\"id\":\"dGFnOjMyOTgyOTIyZTcwNTIzODY3ZTE1NTg2OTBhNDMzMDdl\",\"name\":\"secret-scanning\",\"displayName\":null},{\"id\":\"dGFnOmE4OGFmZmNlNzQ2ODhmMTRlNTc5ZTk5MzkxNWFjODZj\",\"name\":\"security-tools\",\"displayName\":\"安全\"},{\"id\":\"dGFnOjZhODIzZjJiOWRiNmQ5MGIxYTE3YWNlYThlYjFlMmU0\",\"name\":\"certificate-management\",\"displayName\":null},{\"id\":\"dGFnOmIzNzc4ODA3MjY3YWVkMjUwMmMwNDM2MjNlYzhmMmQ3\",\"name\":\"private-ca\",\"displayName\":null},{\"id\":\"dGFnOmMxZjIwMTEwMmJhZGIxYjRkM2Q4NjNmNTI1OTY5Zjdl\",\"name\":\"pki\",\"displayName\":null},{\"id\":\"dGFnOjUzYmNlNGYxZGZhMGZlOGU3Y2ExMjZmOTFiMzVkM2E2\",\"name\":\"acme\",\"displayName\":null},{\"id\":\"dGFnOjY5OTllOGI0MWIxZGExMWQzNWU0ZDg4YmExMTAwYzVj\",\"name\":\"node-js\",\"displayName\":\"Node.js\"},{\"id\":\"dGFnOjMyNzY5Mzc3ZWFiYjA2Y2JmOTUwZjc4NGZmMzI3ZjAz\",\"name\":\"secrets-management\",\"displayName\":null},{\"id\":\"dGFnOjE4NGFhMDc3ZGYwOGI5MGFjOWZlMjgyY2NlYWEzMjVl\",\"name\":\"vault\",\"displayName\":null}],\"createAt\":\"2022-08-05T13:50:00Z\",\"updateAt\":\"2025-07-27T18:47:33Z\",\"user\":null,\"chinaFlag\":0,\"commentCount\":0,\"isArchived\":false,\"status\":0}]}}}},\"children\":[\"$\",\"$L18\",null,{}]}]}],[\"$\",\"section\",null,{\"className\":\"bg-white pb-5\",\"children\":[\"$\",\"div\",null,{\"className\":\"md:container lg:container mx-auto pt-0 mt-0\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-row justify-center\",\"children\":[\"$\",\"div\",null,{\"className\":\"basis-full lg:basis-9/12\",\"children\":[\"$\",\"section\",null,{\"children\":[[\"$\",\"h2\",null,{\"className\":\"color-fg-default f4 mb-2 px-2 font-medium\",\"children\":\"活跃组织\"}],[\"$\",\"p\",null,{\"className\":\"px-2 text-slate-500 text-sm\",\"children\":\"90%+, 全球 Top100 公司正在使用GitHub\"}],[\"$\",\"div\",null,{\"className\":\"d-flex flex-wrap\",\"children\":[[\"$\",\"div\",\"microsoft\",{\"className\":\"col-4 col-sm-2 px-3 mt-3\",\"children\":[\"$\",\"$L6\",null,{\"href\":\"/org/microsoft\",\"className\":\"text-slate-700\",\"children\":[[\"$\",\"img\",null,{\"style\":{\"width\":40},\"src\":\"https://static.github-zh.com/github_avatars/microsoft?size=48\"}],[\"$\",\"p\",null,{\"children\":\"微软\"}]]}]}],[\"$\",\"div\",\"google\",{\"className\":\"col-4 col-sm-2 px-3 mt-3\",\"children\":[\"$\",\"$L6\",null,{\"href\":\"/org/google\",\"className\":\"text-slate-700\",\"children\":[[\"$\",\"img\",null,{\"style\":{\"width\":40},\"src\":\"https://static.github-zh.com/github_avatars/google?size=48\"}],[\"$\",\"p\",null,{\"children\":\"Google\"}]]}]}],[\"$\",\"div\",\"facebook\",{\"className\":\"col-4 col-sm-2 px-3 mt-3\",\"children\":[\"$\",\"$L6\",null,{\"href\":\"/org/facebook\",\"className\":\"text-slate-700\",\"children\":[[\"$\",\"img\",null,{\"style\":{\"width\":40},\"src\":\"https://static.github-zh.com/github_avatars/facebook?size=48\"}],[\"$\",\"p\",null,{\"children\":\"Meta\"}]]}]}],[\"$\",\"div\",\"apache\",{\"className\":\"col-4 col-sm-2 px-3 mt-3\",\"children\":[\"$\",\"$L6\",null,{\"href\":\"/org/apache\",\"className\":\"text-slate-700\",\"children\":[[\"$\",\"img\",null,{\"style\":{\"width\":40},\"src\":\"https://static.github-zh.com/github_avatars/apache?size=48\"}],[\"$\",\"p\",null,{\"children\":\"Apache\"}]]}]}],[\"$\",\"div\",\"alibaba\",{\"className\":\"col-4 col-sm-2 px-3 mt-3\",\"children\":[\"$\",\"$L6\",null,{\"href\":\"/org/alibaba\",\"className\":\"text-slate-700\",\"children\":[[\"$\",\"img\",null,{\"style\":{\"width\":40},\"src\":\"https://static.github-zh.com/github_avatars/alibaba?size=48\"}],[\"$\",\"p\",null,{\"children\":\"阿里巴巴\"}]]}]}],[\"$\",\"div\",\"Tencent\",{\"className\":\"col-4 col-sm-2 px-3 mt-3\",\"children\":[\"$\",\"$L6\",null,{\"href\":\"/org/Tencent\",\"className\":\"text-slate-700\",\"children\":[[\"$\",\"img\",null,{\"style\":{\"width\":40},\"src\":\"https://static.github-zh.com/github_avatars/Tencent?size=48\"}],[\"$\",\"p\",null,{\"children\":\"腾讯\"}]]}]}],[\"$\",\"div\",\"didi\",{\"className\":\"col-4 col-sm-2 px-3 mt-3\",\"children\":[\"$\",\"$L6\",null,{\"href\":\"/org/didi\",\"className\":\"text-slate-700\",\"children\":[[\"$\",\"img\",null,{\"style\":{\"width\":40},\"src\":\"https://static.github-zh.com/github_avatars/didi?size=48\"}],[\"$\",\"p\",null,{\"children\":\"滴滴出行\"}]]}]}],[\"$\",\"div\",\"bilibili\",{\"className\":\"col-4 col-sm-2 px-3 mt-3\",\"children\":[\"$\",\"$L6\",null,{\"href\":\"/org/bilibili\",\"className\":\"text-slate-700\",\"children\":[[\"$\",\"img\",null,{\"style\":{\"width\":40},\"src\":\"https://static.github-zh.com/github_avatars/bilibili?size=48\"}],[\"$\",\"p\",null,{\"children\":\"哔哩哔哩\"}]]}]}],[\"$\",\"div\",\"bytedance\",{\"className\":\"col-4 col-sm-2 px-3 mt-3\",\"children\":[\"$\",\"$L6\",null,{\"href\":\"/org/bytedance\",\"className\":\"text-slate-700\",\"children\":[[\"$\",\"img\",null,{\"style\":{\"width\":40},\"src\":\"https://static.github-zh.com/github_avatars/bytedance?size=48\"}],[\"$\",\"p\",null,{\"children\":\"字节跳动\"}]]}]}],[\"$\",\"div\",\"adobe\",{\"className\":\"col-4 col-sm-2 px-3 mt-3\",\"children\":[\"$\",\"$L6\",null,{\"href\":\"/org/adobe\",\"className\":\"text-slate-700\",\"children\":[[\"$\",\"img\",null,{\"style\":{\"width\":40},\"src\":\"https://static.github-zh.com/github_avatars/adobe?size=48\"}],[\"$\",\"p\",null,{\"children\":\"Adobe\"}]]}]}],[\"$\",\"div\",\"baidu\",{\"className\":\"col-4 col-sm-2 px-3 mt-3\",\"children\":[\"$\",\"$L6\",null,{\"href\":\"/org/baidu\",\"className\":\"text-slate-700\",\"children\":[[\"$\",\"img\",null,{\"style\":{\"width\":40},\"src\":\"https://static.github-zh.com/github_avatars/baidu?size=48\"}],[\"$\",\"p\",null,{\"children\":\"百度\"}]]}]}],[\"$\",\"div\",\"Meituan-Dianping\",{\"className\":\"col-4 col-sm-2 px-3 mt-3\",\"children\":[\"$\",\"$L6\",null,{\"href\":\"/org/Meituan-Dianping\",\"className\":\"text-slate-700\",\"children\":[[\"$\",\"img\",null,{\"style\":{\"width\":40},\"src\":\"https://static.github-zh.com/github_avatars/Meituan-Dianping?size=48\"}],[\"$\",\"p\",null,{\"children\":\"美团\"}]]}]}]]}]]}]}]}]}]}],[\"$\",\"section\",null,{\"className\":\"py-5\",\"style\":{\"background\":\"#f6f8fa\"},\"children\":[\"$\",\"div\",null,{\"className\":\"md:container lg:container mx-auto pt-0 mt-0\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-row justify-center\",\"children\":[\"$\",\"div\",null,{\"className\":\"basis-full lg:basis-9/12\",\"children\":[[\"$\",\"section\",null,{\"className\":\"mt-4 mb-5\",\"children\":[[\"$\",\"h2\",null,{\"className\":\"color-fg-default f4 mb-2 px-2 font-medium\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"rgb(220, 0, 78)\"},\"children\":[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"aria-hidden\":\"true\",\"tabIndex\":\"$undefined\",\"focusable\":\"false\",\"aria-label\":\"$undefined\",\"aria-labelledby\":\"$undefined\",\"className\":\"octicon octicon-heart\",\"role\":\"$undefined\",\"viewBox\":\"0 0 16 16\",\"width\":16,\"height\":16,\"fill\":\"currentColor\",\"id\":\"$undefined\",\"style\":{\"display\":\"inline-block\",\"userSelect\":\"none\",\"verticalAlign\":\"text-bottom\",\"overflow\":\"visible\"},\"children\":[null,[\"$\",\"path\",null,{\"d\":\"m8 14.25.345.666a.75.75 0 0 1-.69 0l-.008-.004-.018-.01a7.152 7.152 0 0 1-.31-.17 22.055 22.055 0 0 1-3.434-2.414C2.045 10.731 0 8.35 0 5.5 0 2.836 2.086 1 4.25 1 5.797 1 7.153 1.802 8 3.02 8.847 1.802 10.203 1 11.75 1 13.914 1 16 2.836 16 5.5c0 2.85-2.045 5.231-3.885 6.818a22.066 22.066 0 0 1-3.744 2.584l-.018.01-.006.003h-.002ZM4.25 2.5c-1.336 0-2.75 1.164-2.75 3 0 2.15 1.58 4.144 3.365 5.682A20.58 20.58 0 0 0 8 13.393a20.58 20.58 0 0 0 3.135-2.211C12.92 9.644 14.5 7.65 14.5 5.5c0-1.836-1.414-3-2.75-3-1.373 0-2.609.986-3.029 2.456a.749.749 0 0 1-1.442 0C6.859 3.486 5.623 2.5 4.25 2.5Z\"}]]}]}],\" 最受欢迎的页面\"]}],[\"$\",\"div\",null,{\"className\":\"d-flex flex-wrap\",\"children\":[[\"$\",\"div\",null,{\"className\":\"col-6 col-sm-4 p-2\",\"children\":[\"$\",\"$L6\",null,{\"href\":\"/collections/learn-to-code\",\"children\":\"面试与学习资源\"}]}],[\"$\",\"div\",null,{\"className\":\"col-6 col-sm-4 p-2\",\"children\":[\"$\",\"$L6\",null,{\"href\":\"/collections/for-beginner\",\"children\":\"新手必看，学习路线图\"}]}],[\"$\",\"div\",null,{\"className\":\"col-6 col-sm-4 p-2\",\"children\":[\"$\",\"$L6\",null,{\"href\":\"/top-china\",\"children\":\"中文项目排行榜\"}]}],[\"$\",\"div\",null,{\"className\":\"col-6 col-sm-4 p-2\",\"children\":[\"$\",\"$L6\",null,{\"href\":\"/real-world\",\"children\":\"实战项目\"}]}],[\"$\",\"div\",null,{\"className\":\"col-6 col-sm-4 p-2\",\"children\":[\"$\",\"$L6\",null,{\"href\":\"/collections/awesome\",\"children\":\"Awesome系列集合\"}]}],[\"$\",\"div\",null,{\"className\":\"col-6 col-sm-4 p-2\",\"children\":[\"$\",\"$L6\",null,{\"href\":\"/trends/monthly\",\"children\":\"GitHub趋势月报\"}]}]]}]]}],[\"$\",\"h2\",null,{\"className\":\"color-fg-default f4 mb-2 px-2 font-medium\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"aria-hidden\":\"true\",\"tabIndex\":\"$undefined\",\"focusable\":\"false\",\"aria-label\":\"$undefined\",\"aria-labelledby\":\"$undefined\",\"className\":\"mr-1\",\"role\":\"$undefined\",\"viewBox\":\"0 0 16 16\",\"width\":16,\"height\":16,\"fill\":\"currentColor\",\"id\":\"$undefined\",\"style\":{\"display\":\"inline-block\",\"userSelect\":\"none\",\"verticalAlign\":\"text-bottom\",\"overflow\":\"visible\"},\"children\":[null,[\"$\",\"path\",null,{\"d\":\"M6.368 1.01a.75.75 0 0 1 .623.859L6.57 4.5h3.98l.46-2.868a.75.75 0 0 1 1.48.237L12.07 4.5h2.18a.75.75 0 0 1 0 1.5h-2.42l-.64 4h2.56a.75.75 0 0 1 0 1.5h-2.8l-.46 2.869a.75.75 0 0 1-1.48-.237l.42-2.632H5.45l-.46 2.869a.75.75 0 0 1-1.48-.237l.42-2.632H1.75a.75.75 0 0 1 0-1.5h2.42l.64-4H2.25a.75.75 0 0 1 0-1.5h2.8l.46-2.868a.75.75 0 0 1 .858-.622ZM9.67 10l.64-4H6.33l-.64 4Z\"}]]}],[\"$\",\"a\",null,{\"href\":\"/topics\",\"children\":\"按主题浏览\"}]]}],[\"$\",\"$L19\",null,{\"topics\":[{\"display_name\":\"Awesome Lists\",\"topic\":\"awesome\",\"aliases\":[\"awesome-lists\",\"awesome-list\"],\"created_by\":\"Sindre Sorhus and the community\",\"github_url\":\"https://github.com/sindresorhus/awesome\",\"logo\":\"/topics-images/awesome/awesome.png\",\"released\":\"July 11, 2014\",\"short_description\":\"Awesome List 是由开源社区创建和维护的资源列表、列表中收集了各种有用的资源，主题从书籍到应用程序不等，几乎你想要的都能在这里找到。\",\"url\":\"https://awesome.re/\",\"description\":\"\\nAwesome List 是由开源社区创建和维护的资源列表、列表中收集了各种有用的资源，主题从书籍到应用程序不等，几乎你想要的都能在这里找到。\\n\"},{\"aliases\":[\"google-chrome\"],\"related\":[\"firefox,safari,edge,opera,chromium,browser,chrome-extension\"],\"created_by\":\"Google\",\"display_name\":\"Chrome\",\"github_url\":\"https://github.com/googlechrome\",\"logo\":\"/topics-images/chrome/chrome.png\",\"released\":\"September 2, 2008\",\"short_description\":\"Chrome 是 Google 公司开发的 Web 浏览器\",\"topic\":\"chrome\",\"url\":\"https://www.google.com/chrome/\",\"description\":\"\\n\\nChrome is the most popular web browser worldwide as of mid-2017, made by the tech company Google. It's available for most operating systems including Windows, macOS, and Linux and on multiple platforms such as the desktop, phones, and tablets.\\n\\nChrome boasts a minimalistic UI and was the first browser to feature \\\"tabs\\\" above the address bar, a convention that was later implemented in other browsers. Other popular features include things such as Incognito mode, tab sandboxing, and a Web Store with extensions and themes.\\n\\nAlthough Chrome is not open source, the majority of the source code is available under the Chromium moniker.\\n\"},{\"aliases\":[\"quality\"],\"display_name\":\"Code quality\",\"short_description\":\"Automate your code review with style, quality, security, and test‑coverage checks when you need them.\",\"topic\":\"code-quality\",\"description\":\"\\nAutomate your code review with style, quality, security, and test‑coverage checks when you need them most. Code quality is intended to keep complexity down and runtime up.\\n\"},{\"display_name\":\"编译器\",\"short_description\":\"Compilers are software that translate higher-level programming languages to lower-level languages (e.g. machine code).\",\"topic\":\"compiler\",\"related\":[\"interpreter\",\"gcc\",\"fortran\"],\"wikipedia_url\":\"https://en.wikipedia.org/wiki/Compiler\",\"description\":\"\\nCompilers are software that translate higher-level (more human readable) programming languages to lower-level languages (e.g. machine code). The processor executes machine code, which indicates when binary high and low signals are required in the arithmetic logic unit of the processor. Examples of compiled languages include BASIC, Fortran, C++, C, and Java.\\n\"},{\"aliases\":[\"css3\"],\"created_by\":\"Håkon Wium, Lie Bert Bos\",\"display_name\":\"CSS\",\"logo\":\"/topics-images/css/css.png\",\"released\":\"December 17, 1996\",\"short_description\":\"CSS(层叠样式表) 是Web开发的核心语言之一，用于描述网页(HTML)或文档的外观样式。\",\"topic\":\"css\",\"url\":\"https://www.w3.org/Style/CSS/Overview.en.html\",\"wikipedia_url\":\"https://en.wikipedia.org/wiki/Cascading_Style_Sheets\",\"description\":\"\\nCSS(层叠样式表) 是Web开发的核心语言之一，用于描述网页(HTML)或文档的外观样式。\\n比如，可以更改内容的字体、颜色、大小以及间距，调整布局，或是添加动画及赋予内容其他装饰性的特征。\"},{\"aliases\":[\"db\",\"databases\"],\"display_name\":\"数据库\",\"short_description\":\"数据库是计算机（通常是服务器）中以一定方式储存，能予多个用户共享，在与应用程序彼此独立的结构化数据集合。\",\"topic\":\"database\",\"logo\":\"/topics-images/database/database.png\",\"wikipedia_url\":\"https://en.wikipedia.org/wiki/Database\",\"related\":[\"dbms\",\"sql\",\"relational-database\",\"nosql\",\"document-oriented\",\"in-memory-database\",\"data-warehouse\",\"graphdb\",\"spatial-database\",\"crud\"],\"description\":\"\\n数据库是计算机（通常是服务器）中以一定方式储存，能予多个用户共享，在与应用程序彼此独立的结构化数据集合。一个数据库由多个表空间构成。\"},{\"aliases\":[\"frontend-developer\"],\"display_name\":\"前端\",\"short_description\":\"Front end is the programming and layout that people see and interact with.\",\"topic\":\"frontend\",\"wikipedia_url\":\"https://en.wikipedia.org/wiki/Front-end_web_development\",\"description\":\"\\nFront-end development is the process of producing HTML, CSS, and JavaScript for web application visuals and interactions. Basically it is a language that is executed in client.\\n\"},{\"aliases\":[\"js\"],\"created_by\":\"Brendan Eich\",\"display_name\":\"JavaScript\",\"logo\":\"/topics-images/javascript/javascript.png\",\"released\":\"December 4, 1995\",\"short_description\":\"JavaScript(简称JS) 是一种非常流行的脚本语言，主要用于Web开发。\",\"topic\":\"javascript\",\"url\":\"https://developer.mozilla.org/en-US/docs/Web/JavaScript\",\"related\":[\"nodejs\"],\"wikipedia_url\":\"https://en.wikipedia.org/wiki/JavaScript\",\"description\":\"\\nJavaScript(简称JS) 是一种非常流行的解释型脚本编程语言、单线程。\\n虽然作为 Web 网页中的脚本语言被人所熟知，但是它也被用到了很多非浏览器环境中，例如 Node.js、Apache CouchDB、Adobe Acrobat 等。\"},{\"aliases\":[\"node\",\"node-js\"],\"created_by\":\"Ryan Dahl\",\"display_name\":\"Node.js\",\"github_url\":\"https://github.com/nodejs\",\"logo\":\"/topics-images/nodejs/nodejs.png\",\"released\":\"May 27, 2009\",\"short_description\":\"Node.js 是一个 JavaScript 运行环境。\",\"topic\":\"nodejs\",\"url\":\"https://nodejs.org/en/\",\"wikipedia_url\":\"https://en.wikipedia.org/wiki/Node.js\",\"related\":[\"javascript\",\"express\",\"mean\",\"meteor\",\"next\",\"v8\",\"event-driven-architecture\",\"event-loop\",\"npm\",\"deno\"],\"description\":\"\\n\\n**Node.js** 是一个 JavaScript 运行环境。在 Node.js 出现之前，JavaScript只是一种存在于 Web 浏览器中的语言，但 Node.js 项目扩大了它的影响力，并使其成为世界上最受欢迎的编程语言。Node.js 拓展了具有 Web 开发经验的人们的创造潜力，使新一代开发人员能够创建服务器、命令行工具、桌面应用程序甚至机器人。\"},{\"aliases\":[\"npmjs\",\"npm-package\"],\"created_by\":\"Isaac Z. Schlueter\",\"display_name\":\"npm\",\"github_url\":\"https://github.com/npm\",\"logo\":\"/topics-images/npm/npm.png\",\"released\":\"January 12, 2010\",\"short_description\":\"npm 是 Node.js 默认的包管理器。\",\"topic\":\"npm\",\"url\":\"https://www.npmjs.com/\",\"wikipedia_url\":\"https://en.wikipedia.org/wiki/Npm_(software)\",\"description\":\"\\nnpm 是 Node.js 默认的包管理器。\"},{\"display_name\":\"项目管理\",\"short_description\":\"项目管理是领导一个团队在规定时间内实现目标和达到成功标准的过程。\",\"topic\":\"project-management\",\"wikipedia_url\":\"https://en.wikipedia.org/wiki/Project_management\",\"related\":[\"kanban\",\"scrum\",\"kanboard\",\"agile\",\"roadmap\",\"gantt\"],\"description\":\"\\n**项目管理** 是启动、规划、执行、监控、结束团队工作的过程，确保项目能够按时、按质、按预算地完成。\"},{\"aliases\":[\"python3\",\"python2\",\"python-3\",\"python-2\",\"python27\",\"python-3-6\",\"python-3-5\",\"py\"],\"created_by\":\"Guido van Rossum\",\"display_name\":\"Python\",\"github_url\":\"https://github.com/python\",\"logo\":\"/topics-images/python/python.png\",\"related\":[\"language\",\"ruby\"],\"released\":\"February 20, 1991\",\"short_description\":\"Python 是一门动态类型编程语言\",\"topic\":\"python\",\"url\":\"https://www.python.org/\",\"wikipedia_url\":\"https://en.wikipedia.org/wiki/Python_(programming_language)\",\"description\":\"\\nPython 是一门具有动态类型和垃圾回收功能的高级语言，由 Guido van Rossum 于 80 年代末开发，以取代 ABC。\\n\\n其设计哲学，强调代码的可读性和简洁的语法，尤其是使用空格缩进来划分代码块。相比于C语言或Java，Python让开发者能够用更少的代码表达想法。\"},{\"aliases\":[\"reactjs\",\"react-js\"],\"created_by\":\"Jordan Walke\",\"display_name\":\"React\",\"github_url\":\"https://github.com/facebook/react\",\"logo\":\"/topics-images/react/react.png\",\"related\":[\"vue\",\"angular\",\"react-native\",\"nextjs\"],\"released\":\"March 2013\",\"short_description\":\"React 是一个构建用户界面的 JavaScript 库。\",\"topic\":\"react\",\"url\":\"https://reactjs.org/\",\"wikipedia_url\":\"https://en.wikipedia.org/wiki/React_(JavaScript_library)\",\"description\":\"\\nReact（又称 React.js 或 ReactJS）是一个 JavaScript 库，可以简化交互式用户界面的开发，由 FaceBook 开源。\"},{\"aliases\":[\"react-native-app\"],\"created_by\":\"Facebook\",\"display_name\":\"React Native\",\"github_url\":\"https://github.com/facebook/react-native\",\"logo\":\"/topics-images/react-native/react-native.png\",\"related\":[\"reactjs\"],\"released\":\"January 2015\",\"short_description\":\"React Native 是一个用于开发移动应用的JavaScript框架，由Facebook开源。\",\"topic\":\"react-native\",\"url\":\"https://reactnative.dev/\",\"wikipedia_url\":\"https://en.wikipedia.org/wiki/React_Native\",\"description\":\"\\nReact Native 是一个由 Facebook 开发的 JavaScript 移动应用框架。\\n它允许开发者使用 JavaScript 技术开发 Android 和 iOS 应用，并支持在 Web 和App之间复用代码。\"},{\"created_by\":\"Martin Odersky\",\"display_name\":\"Scala\",\"logo\":\"/topics-images/scala/scala.png\",\"released\":\"January 20, 2004\",\"short_description\":\"Scala 是一门面向对象、函数式编程语言，运行于Java平台。\",\"topic\":\"scala\",\"url\":\"http://www.scala-lang.org/\",\"wikipedia_url\":\"https://en.wikipedia.org/wiki/Scala_(programming_language)\",\"description\":\"\\nScala 是一门多范式的编程语言，设计初衷是要集成面向对象编程和函数式编程的各种特性。\\n\\nScala 运行于Java平台（Java虚拟机），并兼容现有的Java程序。\"},{\"aliases\":[\"typescript2\"],\"created_by\":\"Microsoft\",\"display_name\":\"TypeScript\",\"github_url\":\"https://github.com/Microsoft/TypeScript\",\"logo\":\"/topics-images/typescript/typescript.png\",\"related\":[\"language\",\"javascript\",\"nodejs\",\"es6\"],\"released\":\"October 1, 2012\",\"short_description\":\"TypeScript 是JavaScript的严格超集，可以编译为纯 JavaScript。\",\"topic\":\"typescript\",\"url\":\"https://www.typescriptlang.org/\",\"wikipedia_url\":\"https://en.wikipedia.org/wiki/TypeScript\",\"description\":\"\\nTypeScript 是微软开发的一种开源编程语言，于 2012 年首次发布。它旨在为 JavaScript 添加类型安全性，\\n同时尽可能符合 ECMAScript 标准的语法和语义。\\n\\n它是 JavaScript 编程语言的语法超集。可以编译为纯 JavaScript，这意味着它可以用于任何 JavaScript 环境。\"}]}]]}]}]}]}],[\"$\",\"section\",null,{\"className\":\"bg-white p-1 py-5\",\"children\":[\"$\",\"div\",null,{\"className\":\"container-lg\",\"style\":{\"textAlign\":\"center\"},\"children\":[[\"$\",\"h2\",null,{\"className\":\"text-lg mb-2 text-normal\",\"children\":\"GitHub 插件推荐\"}],[\"$\",\"p\",null,{\"className\":\"mb-2 color-fg-subtle\",\"children\":\"工欲善其事必先利其器，善于利用插件能让您事半功倍。\"}],[\"$\",\"div\",null,{\"className\":\"d-flex flex-wrap\",\"children\":[[\"$\",\"div\",null,{\"className\":\"col-6 col-sm-4\",\"children\":[\"$\",\"div\",null,{\"style\":{\"padding\":15},\"children\":[[\"$\",\"h4\",null,{\"className\":\"h4 mb-1\",\"children\":[\"$\",\"a\",null,{\"className\":\"color-fg-accent\",\"href\":\"https://github.com/sindresorhus/refined-github\",\"children\":\"Refined GitHub\"}]}],[\"$\",\"p\",null,{\"className\":\"color-fg-subtle\",\"children\":\"优化GitHub功能，使其变得更好用。提供了诸如“支持下载某个目录”等实用小功能。\"}]]}]}],[\"$\",\"div\",null,{\"className\":\"col-6 col-sm-4\",\"children\":[\"$\",\"div\",null,{\"style\":{\"padding\":15},\"children\":[[\"$\",\"h4\",null,{\"className\":\"h4 mb-1\",\"children\":[\"$\",\"a\",null,{\"className\":\"color-fg-accent\",\"href\":\"https://github.com/k1995/github-i18n-plugin\",\"children\":\"GitHub汉化插件\"}]}],[\"$\",\"p\",null,{\"className\":\"color-fg-subtle\",\"children\":\"汉化GitHub菜单栏，并支持将英文项目简介翻译为中文。\"}]]}]}],[\"$\",\"div\",null,{\"className\":\"col-12 col-sm-4\",\"children\":[\"$\",\"div\",null,{\"style\":{\"padding\":15},\"children\":[[\"$\",\"h4\",null,{\"className\":\"h4 mb-1\",\"children\":[\"$\",\"a\",null,{\"className\":\"color-fg-accent\",\"href\":\"https://greasyfork.org/zh-CN/scripts/412245-github-%E5%A2%9E%E5%BC%BA-%E9%AB%98%E9%80%9F%E4%B8%8B%E8%BD%BD\",\"children\":\"GitHub高速下载插件\"}]}],[\"$\",\"p\",null,{\"className\":\"color-fg-subtle\",\"children\":\"是否感到在GitHub上下载文件巨慢？该插件提供高速下载代理服务。\"}]]}]}]]}],[\"$\",\"br\",null,{}],[\"$\",\"p\",null,{\"className\":\"color-fg-subtle\",\"children\":[\"寻找更多插件，请戳\",[\"$\",\"$L6\",null,{\"className\":\"color-fg-accent\",\"href\":\"/post/chrome-extensions-for-developer\",\"children\":\"这里\"}]]}]]}]}]]}]]}]\n"])</script></body></html><style data-styled="" data-styled-version="5.3.11"></style>