<?php
/**
 * HawaiiHub 简化采集器
 * 基于CSS选择器的轻量级采集解决方案
 */

require_once 'vendor/autoload.php';
use Goutte\Client;
use Symfony\Component\DomCrawler\Crawler;

class HawaiiHubSpider {
    private $client;
    private $config;
    
    public function __construct() {
        $this->client = new Client();
        $this->client->setServerParameter('HTTP_USER_AGENT', 'HawaiiHub Spider 1.0');
    }
    
    /**
     * 加载采集配置
     */
    public function loadConfig($configFile) {
        $this->config = json_decode(file_get_contents($configFile), true);
        return $this;
    }
    
    /**
     * 执行采集任务
     */
    public function crawl($siteName) {
        if (!isset($this->config[$siteName])) {
            throw new Exception("Site configuration not found: $siteName");
        }
        
        $siteConfig = $this->config[$siteName];
        $articles = [];
        
        try {
            // 获取列表页
            $crawler = $this->client->request('GET', $siteConfig['list_url']);
            
            // 提取文章链接
            $links = $crawler->filter($siteConfig['list_selector'])->each(function ($node) {
                return $node->attr('href');
            });
            
            // 采集每篇文章
            foreach (array_slice($links, 0, 10) as $link) { // 限制采集数量
                $fullUrl = $this->resolveUrl($link, $siteConfig['list_url']);
                $article = $this->crawlArticle($fullUrl, $siteConfig['rules']);
                if ($article) {
                    $articles[] = $article;
                }
                sleep(1); // 避免过于频繁的请求
            }
            
        } catch (Exception $e) {
            error_log("Crawl error for $siteName: " . $e->getMessage());
        }
        
        return $articles;
    }
    
    /**
     * 采集单篇文章
     */
    private function crawlArticle($url, $rules) {
        try {
            $crawler = $this->client->request('GET', $url);
            $article = ['url' => $url];
            
            foreach ($rules as $field => $selector) {
                $nodes = $crawler->filter($selector);
                if ($nodes->count() > 0) {
                    $article[$field] = $field === 'content' ? 
                        $nodes->html() : 
                        trim($nodes->text());
                }
            }
            
            return $article;
            
        } catch (Exception $e) {
            error_log("Article crawl error for $url: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 解析相对URL为绝对URL
     */
    private function resolveUrl($url, $baseUrl) {
        if (strpos($url, 'http') === 0) {
            return $url;
        }
        
        $parsedBase = parse_url($baseUrl);
        $scheme = $parsedBase['scheme'];
        $host = $parsedBase['host'];
        
        if (strpos($url, '/') === 0) {
            return "$scheme://$host$url";
        }
        
        return "$scheme://$host/" . ltrim($url, '/');
    }
    
    /**
     * 保存采集结果到数据库
     */
    public function saveToDatabase($articles, $categoryId = 1) {
        global $dsql;
        
        foreach ($articles as $article) {
            if (empty($article['title']) || empty($article['content'])) {
                continue;
            }
            
            $title = addslashes($article['title']);
            $content = addslashes($article['content']);
            $source = addslashes($article['source'] ?? '');
            $author = addslashes($article['author'] ?? '');
            $pubdate = time();
            
            $sql = "INSERT INTO `#@__articlelist` 
                    (`title`, `typeid`, `source`, `writer`, `pubdate`, `arcrank`) 
                    VALUES ('$title', '$categoryId', '$source', '$author', '$pubdate', '1')";
            
            $sqls = $dsql->SetQuery($sql);
            $aid = $dsql->dsqlOper($sqls, "lastid");
            
            if ($aid) {
                $contentSql = "INSERT INTO `#@__article` (`aid`, `body`) VALUES ('$aid', '$content')";
                $contentSqls = $dsql->SetQuery($contentSql);
                $dsql->dsqlOper($contentSqls, "update");
            }
        }
    }
}

// 使用示例
try {
    $spider = new HawaiiHubSpider();
    $spider->loadConfig('spider_config.json');
    
    // 采集夏威夷新闻
    $articles = $spider->crawl('hawaii_news_now');
    $spider->saveToDatabase($articles, 1);
    
    echo "Successfully crawled " . count($articles) . " articles\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
