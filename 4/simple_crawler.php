<?php
/**
 * HawaiiHub 简化采集模块
 * 在现有插件基础上添加的新采集功能
 */
define('HUONIAOADMIN', ".");
require_once 'common.php';

$dsql = new dsql($dbo);
$userLogin = new userLogin($dbo);

if($userLogin->getUserID() == -1){
    header("location:" . $cfg_secureAccess.$cfg_basehost);
    exit();
}

class SimpleCrawler {
    private $dsql;
    private $config;
    
    public function __construct($dsql) {
        $this->dsql = $dsql;
        $this->loadConfig();
    }
    
    /**
     * 加载预设的采集配置
     */
    private function loadConfig() {
        $this->config = [
            'hawaii_news_now' => [
                'name' => 'Hawaii News Now',
                'list_url' => 'https://www.hawaiinewsnow.com/news/',
                'list_pattern' => '/<h3[^>]*><a[^>]*href="([^"]*)"[^>]*>([^<]*)<\/a><\/h3>/',
                'content_patterns' => [
                    'title' => '/<h1[^>]*class="[^"]*headline[^"]*"[^>]*>([^<]*)<\/h1>/',
                    'content' => '/<div[^>]*class="[^"]*story-body[^"]*"[^>]*>(.*?)<\/div>/s',
                    'date' => '/<time[^>]*class="[^"]*timestamp[^"]*"[^>]*>([^<]*)<\/time>/',
                    'author' => '/<span[^>]*class="[^"]*byline[^"]*"[^>]*>([^<]*)<\/span>/'
                ]
            ],
            'honolulu_star' => [
                'name' => 'Honolulu Star-Advertiser',
                'list_url' => 'https://www.staradvertiser.com/category/breaking-news/',
                'list_pattern' => '/<h2[^>]*class="[^"]*post-title[^"]*"[^>]*><a[^>]*href="([^"]*)"[^>]*>([^<]*)<\/a><\/h2>/',
                'content_patterns' => [
                    'title' => '/<h1[^>]*class="[^"]*entry-title[^"]*"[^>]*>([^<]*)<\/h1>/',
                    'content' => '/<div[^>]*class="[^"]*entry-content[^"]*"[^>]*>(.*?)<\/div>/s',
                    'date' => '/<time[^>]*class="[^"]*entry-date[^"]*"[^>]*>([^<]*)<\/time>/',
                    'author' => '/<span[^>]*class="[^"]*author-name[^"]*"[^>]*>([^<]*)<\/span>/'
                ]
            ]
        ];
    }
    
    /**
     * 执行简化采集
     */
    public function crawl($siteName, $maxArticles = 10) {
        if (!isset($this->config[$siteName])) {
            return ['success' => false, 'message' => '未找到网站配置'];
        }
        
        $config = $this->config[$siteName];
        $articles = [];
        
        try {
            // 获取列表页内容
            $listHtml = $this->getPageContent($config['list_url']);
            if (!$listHtml) {
                return ['success' => false, 'message' => '无法获取列表页内容'];
            }
            
            // 提取文章链接
            preg_match_all($config['list_pattern'], $listHtml, $matches, PREG_SET_ORDER);
            
            $count = 0;
            foreach ($matches as $match) {
                if ($count >= $maxArticles) break;
                
                $url = $this->resolveUrl($match[1], $config['list_url']);
                $title = trim(strip_tags($match[2]));
                
                // 检查是否已存在
                if ($this->articleExists($title)) {
                    continue;
                }
                
                $article = $this->crawlArticle($url, $config['content_patterns']);
                if ($article) {
                    $article['source'] = $config['name'];
                    $article['url'] = $url;
                    $articles[] = $article;
                    $count++;
                }
                
                sleep(1); // 避免请求过于频繁
            }
            
            return [
                'success' => true, 
                'articles' => $articles,
                'count' => count($articles)
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * 采集单篇文章
     */
    private function crawlArticle($url, $patterns) {
        $html = $this->getPageContent($url);
        if (!$html) return null;
        
        $article = [];
        foreach ($patterns as $field => $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $content = isset($matches[1]) ? $matches[1] : '';
                $article[$field] = $field === 'content' ? $content : strip_tags($content);
            }
        }
        
        return !empty($article['title']) && !empty($article['content']) ? $article : null;
    }
    
    /**
     * 获取页面内容
     */
    private function getPageContent($url) {
        // 使用现有的下载函数
        $html = downOnePage($url);
        if (!$html) {
            $html = strToUtf8(curl_get($url));
        }
        if (!$html) {
            $html = strToUtf8(file_get_contents($url));
        }
        return $html;
    }
    
    /**
     * 解析相对URL
     */
    private function resolveUrl($url, $baseUrl) {
        if (strpos($url, 'http') === 0) {
            return $url;
        }
        
        $parsedBase = parse_url($baseUrl);
        $scheme = $parsedBase['scheme'];
        $host = $parsedBase['host'];
        
        if (strpos($url, '/') === 0) {
            return "$scheme://$host$url";
        }
        
        return "$scheme://$host/" . ltrim($url, '/');
    }
    
    /**
     * 检查文章是否已存在
     */
    private function articleExists($title) {
        $title = addslashes($title);
        $sql = $this->dsql->SetQuery("SELECT id FROM `#@__articlelist` WHERE title = '$title' LIMIT 1");
        $result = $this->dsql->dsqlOper($sql, "results");
        return !empty($result);
    }
    
    /**
     * 保存文章到数据库
     */
    public function saveArticles($articles, $categoryId = 1) {
        $saved = 0;
        foreach ($articles as $article) {
            if ($this->saveArticle($article, $categoryId)) {
                $saved++;
            }
        }
        return $saved;
    }
    
    /**
     * 保存单篇文章
     */
    private function saveArticle($article, $categoryId) {
        $title = addslashes($article['title']);
        $content = addslashes($article['content']);
        $source = addslashes($article['source'] ?? '');
        $author = addslashes($article['author'] ?? '');
        $pubdate = time();
        
        // 插入文章列表
        $sql = "INSERT INTO `#@__articlelist` 
                (`title`, `typeid`, `source`, `writer`, `pubdate`, `arcrank`, `cityid`) 
                VALUES ('$title', '$categoryId', '$source', '$author', '$pubdate', '1', '1')";
        
        $sqls = $this->dsql->SetQuery($sql);
        $aid = $this->dsql->dsqlOper($sqls, "lastid");
        
        if ($aid) {
            // 插入文章内容
            $contentSql = "INSERT INTO `#@__article` (`aid`, `body`) VALUES ('$aid', '$content')";
            $contentSqls = $this->dsql->SetQuery($contentSql);
            $this->dsql->dsqlOper($contentSqls, "update");
            return true;
        }
        
        return false;
    }
}

// 处理AJAX请求
if (isset($_GET['action'])) {
    $crawler = new SimpleCrawler($dsql);
    
    switch ($_GET['action']) {
        case 'crawl':
            $site = $_GET['site'] ?? '';
            $maxArticles = intval($_GET['max'] ?? 10);
            
            $result = $crawler->crawl($site, $maxArticles);
            if ($result['success']) {
                $saved = $crawler->saveArticles($result['articles']);
                returnJson([
                    'code' => 200, 
                    'msg' => "成功采集 {$result['count']} 篇文章，保存 $saved 篇",
                    'data' => $result['articles']
                ]);
            } else {
                returnJson(['code' => 201, 'msg' => $result['message']]);
            }
            break;
            
        case 'test':
            $site = $_GET['site'] ?? '';
            $result = $crawler->crawl($site, 3);
            returnJson([
                'code' => $result['success'] ? 200 : 201,
                'msg' => $result['success'] ? '测试成功' : $result['message'],
                'data' => $result['articles'] ?? []
            ]);
            break;
    }
    exit;
}

// 显示页面
$huoniaoTag->template_dir = dirname(__FILE__) . "/tpl";
$huoniaoTag->assign('cfg_staticPath', $cfg_staticPath);
$huoniaoTag->display('./simple_crawler.html');
?>
