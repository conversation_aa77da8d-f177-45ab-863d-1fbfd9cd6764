/* @group Base */
.chzn-container {
  font-size: 12px;
  position: relative;
  display: inline-block;
  zoom: 1;
  *display: inline;
}
.chzn-container .chzn-drop {
  background: #fff;
  border: 1px solid #aaa;
  border-top: 0;
  position: absolute;
  top: 29px;
  left: 0;
  -webkit-box-shadow: 0 4px 5px rgba(0,0,0,.15);
  -moz-box-shadow   : 0 4px 5px rgba(0,0,0,.15);
  -o-box-shadow     : 0 4px 5px rgba(0,0,0,.15);
  box-shadow        : 0 4px 5px rgba(0,0,0,.15);
  z-index: 1010;
}
/* @end */

/* @group Single Chosen */
.chzn-container-single .chzn-single {
  background-color: #ffffff;
  border: 1px solid #ccc;
  -moz-box-shadow: inset 0 1px 2px #ddd;
  -webkit-box-shadow: inset 0 1px 2px #ddd;
  box-shadow: inset 0 1px 2px #ddd;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  position: relative;
  height: 32px;
  line-height: 24px;
  padding: 0 0 0 8px;
  color: #666;
  text-decoration: none;
}
.chzn-container-single .chzn-default {
  color: #999;
}
.chzn-container-single .chzn-single span {
  margin-top: 5px;
  margin-right: 26px;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  -o-text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  text-overflow: ellipsis;
}
.chzn-container-single .chzn-single abbr {
  display: block;
  position: absolute;
  right: 26px;
  top: 6px;
  width: 12px;
  height: 13px;
  font-size: 1px;
  background: url('../../images/ui/chosen-sprite.png') right top no-repeat;
}
.chzn-container-single .chzn-single abbr:hover {
  background-position: right -11px;
}
.chzn-container-single.chzn-disabled .chzn-single abbr:hover {
  background-position: right top;
}
.chzn-container-single .chzn-single div {
  position: absolute;
  right: 0;
  top: 0;
  display: block;
  height: 100%;
  width: 18px;
}
.chzn-container-single .chzn-single div b {
  background: url('../../images/ui/chosen-sprite.png') no-repeat -2px 3px;
  display: block;
  width: 100%;
  height: 100%;
}
.chzn-container-single .chzn-search {
  padding: 3px 4px;
  position: relative;
  margin: 0;
  white-space: nowrap;
  z-index: 1010;
}
.chzn-container-single .chzn-search input {
  background: #fff url('../../images/ui/chosen-sprite.png') no-repeat 100% -21px;
  background: url('../../images/ui/chosen-sprite.png') no-repeat 100% -21px, -webkit-gradient(linear, 0% 0%, 0% 100%, color-stop(1%, #eeeeee), color-stop(15%, #ffffff));
  background: url('../../images/ui/chosen-sprite.png') no-repeat 100% -21px, -webkit-linear-gradient(top, #eeeeee 1%, #ffffff 15%);
  background: url('../../images/ui/chosen-sprite.png') no-repeat 100% -21px, -moz-linear-gradient(top, #eeeeee 1%, #ffffff 15%);
  background: url('../../images/ui/chosen-sprite.png') no-repeat 100% -21px, -o-linear-gradient(top, #eeeeee 1%, #ffffff 15%);
  background: url('../../images/ui/chosen-sprite.png') no-repeat 100% -21px, -ms-linear-gradient(top, #eeeeee 1%, #ffffff 15%);
  background: url('../../images/ui/chosen-sprite.png') no-repeat 100% -21px, linear-gradient(top, #eeeeee 1%, #ffffff 15%);
  margin: 1px 0;
  padding: 3px 20px 3px 5px !important;
  outline: 0;
  border: 1px solid #bbb;
  font-family: sans-serif;
  font-size: 1em;
}
.chzn-container-single .chzn-drop {
  -moz-background-clip   : padding;
  -webkit-background-clip: padding-box;
  background-clip        : padding-box;
}
/* @end */

.chzn-container-single-nosearch .chzn-search input {
  position: absolute;
  left: -9000px;
}

/* @group Multi Chosen */
.chzn-container-multi .chzn-choices {
  background-color: #fff;
  border: 1px solid #ccc;
  margin: 0;
  padding: 0;
  cursor: text;
  overflow: hidden;
  height: auto !important;
  height: 1%;
  position: relative;
  -moz-box-shadow: inset 0 1px 2px #ddd;
  -webkit-box-shadow: inset 0 1px 2px #ddd;
  box-shadow: inset 0 1px 2px #ddd;
}
.chzn-container-multi .chzn-choices li {
  float: left;
  list-style: none;
}
.chzn-container-multi .chzn-choices .search-field {
  white-space: nowrap;
  margin: 0;
  padding: 0;
}
.chzn-container-multi .chzn-choices .search-field input {
  color: #666;
  background: transparent !important;
  border: 0 !important;
  font-family: sans-serif;
  font-size: 100%;
  height: 15px;
  padding: 7px 5px;
  margin: 1px 0;
  outline: 0;
  -webkit-box-shadow: none;
  -moz-box-shadow   : none;
  -o-box-shadow     : none;
  box-shadow        : none;
}
.chzn-container-multi .chzn-choices .search-field .default {
  color: #999;
}
.chzn-container-multi .chzn-choices .search-choice {
  -moz-background-clip   : padding;
  -webkit-background-clip: padding-box;
  background-clip        : padding-box;
  background-color: #e4e4e4;
  color: #666;
  border: 1px solid #bbb;
  line-height: 13px;
  padding: 2px 20px 2px 5px;
  margin: 3px 0 3px 3px;
  position: relative;
  cursor: default;
}
.chzn-container-multi .chzn-choices .search-choice-focus {
  background: #d4d4d4;
}
.chzn-container-multi .chzn-choices .search-choice .search-choice-close {
  display: block;
  position: absolute;
  right: 3px;
  top: 2px;
  width: 12px;
  height: 13px;
  font-size: 1px;
  background: url('../../images/ui/chosen-sprite.png') right top no-repeat;
}
.chzn-container-multi .chzn-choices .search-choice .search-choice-close:hover {
  background-position: right -11px;
}
.chzn-container-multi .chzn-choices .search-choice-focus .search-choice-close {
  background-position: right -11px;
}
/* @end */

/* @group Results */
.chzn-container .chzn-results {
  margin: 0 4px 4px 0;
  max-height: 240px;
  padding: 0 0 0 4px;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.chzn-container-multi .chzn-results {
  margin: -1px 0 0;
  padding: 0;
}
.chzn-container .chzn-results li {
  display: none;
  line-height: 15px;
  padding: 5px 6px;
  margin: 0;
  list-style: none;
}
.chzn-container .chzn-results .active-result {
  cursor: pointer;
  display: list-item;
}
.chzn-container .chzn-results .highlighted {
  background-color: #19729a;
  color: #fff;
}
.chzn-container .chzn-results li em {
  background: #feffde;
  font-style: normal;
}
.chzn-container .chzn-results .highlighted em {
  background: transparent;
}
.chzn-container .chzn-results .no-results {
  background: #f4f4f4;
  display: list-item;
}
.chzn-container .chzn-results .group-result {
  cursor: default;
  color: #999;
  font-weight: bold;
}
.chzn-container .chzn-results .group-option {
  padding-left: 15px;
}
.chzn-container-multi .chzn-drop .result-selected {
  display: none;
}
.chzn-container .chzn-results-scroll {
  background: white;
  margin: 0 4px;
  position: absolute;
  text-align: center;
  width: 321px; /* This should by dynamic with js */
  z-index: 1;
}
.chzn-container .chzn-results-scroll span {
  display: inline-block;
  height: 17px;
  text-indent: -5000px;
  width: 9px;
}
.chzn-container .chzn-results-scroll-down {
  bottom: 0;
}
.chzn-container .chzn-results-scroll-down span {
  background: url('../../images/ui/chosen-sprite.png') no-repeat -4px -3px;
}
.chzn-container .chzn-results-scroll-up span {
  background: url('../../images/ui/chosen-sprite.png') no-repeat -22px -3px;
}
/* @end */

/* @group Active  */
.chzn-container-active .chzn-single {
  -webkit-box-shadow: 0 0 5px rgba(0,0,0,.1);
  -moz-box-shadow   : 0 0 5px rgba(0,0,0,.1);
  -o-box-shadow     : 0 0 5px rgba(0,0,0,.1);
  box-shadow        : 0 0 5px rgba(0,0,0,.1);
}
.chzn-container-active .chzn-single-with-drop {
  border: 1px solid #aaa;
  -webkit-box-shadow: 0 1px 0 #fff inset;
  -moz-box-shadow   : 0 1px 0 #fff inset;
  -o-box-shadow     : 0 1px 0 #fff inset;
  box-shadow        : 0 1px 0 #fff inset;
  background-color: #fff;
  -webkit-border-bottom-left-radius : 0;
  -webkit-border-bottom-right-radius: 0;
  -moz-border-radius-bottomleft : 0;
  -moz-border-radius-bottomright: 0;
  border-bottom-left-radius : 0;
  border-bottom-right-radius: 0;
}
.chzn-container-active .chzn-single-with-drop div {
  background: transparent;
  border-left: none;
}
.chzn-container-active .chzn-single-with-drop div b {
  background-position: -20px 2px;
}
.chzn-container-active .chzn-choices {
  -webkit-box-shadow: 0 0 5px rgba(0,0,0,.1);
  -moz-box-shadow   : 0 0 5px rgba(0,0,0,.1);
  -o-box-shadow     : 0 0 5px rgba(0,0,0,.1);
  box-shadow        : 0 0 5px rgba(0,0,0,.1);
}
.chzn-container-active .chzn-choices .search-field input {
  color: #111 !important;
}
/* @end */

/* @group Disabled Support */
.chzn-disabled {
  cursor: default;
  opacity:0.5 !important;
}
.chzn-disabled .chzn-single {
  cursor: default;
}
.chzn-disabled .chzn-choices .search-choice .search-choice-close {
  cursor: default;
}

/* @group Right to Left */
.chzn-rtl { text-align: right; }
.chzn-rtl .chzn-single { padding: 0 8px 0 0; overflow: visible; }
.chzn-rtl .chzn-single span { margin-left: 26px; margin-right: 0; direction: rtl; }

.chzn-rtl .chzn-single div { left: 3px; right: auto; }
.chzn-rtl .chzn-single abbr {
  left: 26px;
  right: auto;
}
.chzn-rtl .chzn-choices .search-field input { direction: rtl; }
.chzn-rtl .chzn-choices li { float: right; }
.chzn-rtl .chzn-choices .search-choice { padding: 3px 5px 3px 19px; margin: 3px 5px 3px 0; }
.chzn-rtl .chzn-choices .search-choice .search-choice-close { left: 4px; right: auto; background-position: right top;}
.chzn-rtl.chzn-container-single .chzn-results { margin: 0 0 4px 4px; padding: 0 4px 0 0; }
.chzn-rtl .chzn-results .group-option { padding-left: 0; padding-right: 15px; }
.chzn-rtl.chzn-container-active .chzn-single-with-drop div { border-right: none; }
.chzn-rtl .chzn-search input {
  background: #fff url('../../images/ui/chosen-sprite.png') no-repeat -38px -22px;
  background: url('../../images/ui/chosen-sprite.png') no-repeat -38px -22px, -webkit-gradient(linear, 0% 0%, 0% 100%, color-stop(1%, #eeeeee), color-stop(15%, #ffffff));
  background: url('../../images/ui/chosen-sprite.png') no-repeat -38px -22px, -webkit-linear-gradient(top, #eeeeee 1%, #ffffff 15%);
  background: url('../../images/ui/chosen-sprite.png') no-repeat -38px -22px, -moz-linear-gradient(top, #eeeeee 1%, #ffffff 15%);
  background: url('../../images/ui/chosen-sprite.png') no-repeat -38px -22px, -o-linear-gradient(top, #eeeeee 1%, #ffffff 15%);
  background: url('../../images/ui/chosen-sprite.png') no-repeat -38px -22px, -ms-linear-gradient(top, #eeeeee 1%, #ffffff 15%);
  background: url('../../images/ui/chosen-sprite.png') no-repeat -38px -22px, linear-gradient(top, #eeeeee 1%, #ffffff 15%);
  padding: 4px 5px 4px 20px;
  direction: rtl;
}
/* @end */
