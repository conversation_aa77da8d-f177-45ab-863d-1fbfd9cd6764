graph TD
    A[管理员登录后台] --> B[进入采集插件]
    B --> C[创建采集节点]
    C --> D[配置采集规则]
    D --> D1[设置目标网站URL]
    D --> D2[设置标题提取规则]
    D --> D3[设置正文提取规则]
    D --> D4[设置作者/时间规则]
    
    D1 --> E[开始采集]
    D2 --> E
    D3 --> E
    D4 --> E
    
    E --> F[访问目标网站列表页]
    F --> G[提取文章链接列表]
    G --> H[逐个访问文章页面]
    H --> I[按规则提取内容]
    I --> I1[提取标题]
    I --> I2[提取正文]
    I --> I3[提取作者]
    I --> I4[提取发布时间]
    
    I1 --> J[保存到数据库]
    I2 --> J
    I3 --> J
    I4 --> J
    
    J --> K[插入articlelist表<br/>文章基本信息]
    J --> L[插入article表<br/>文章正文内容]
    
    K --> M[管理员点击发布]
    L --> M
    M --> N[文章状态改为已发布]
    
    N --> O[前端网站显示]
    O --> P[用户在首页看到文章]
    P --> Q[用户点击文章标题]
    Q --> R[显示完整文章内容]
    
    style A fill:#e1f5fe
    style O fill:#e8f5e8
    style R fill:#fff3e0