<?php
/**
 * NewsAPI采集计划任务执行文件
 * 用于自动执行夏威夷新闻采集-NewsAPI节点
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025-07-27
 */

// 防止直接访问
if (!defined('IN_HUONIAO')) {
    exit('Access Denied');
}

// 引入必要的文件
require_once(dirname(__FILE__) . '/../common.inc.php');
require_once(dirname(__FILE__) . '/../plugins/spider/spider.class.php');

/**
 * NewsAPI采集执行类
 */
class NewsAPICollection {
    
    private $spider;
    private $logFile;
    
    public function __construct() {
        $this->spider = new Spider();
        $this->logFile = dirname(__FILE__) . '/../../data/logs/newsapi_collection.log';
    }
    
    /**
     * 执行采集任务
     */
    public function execute() {
        $this->log("开始执行NewsAPI采集任务");
        
        try {
            // 查找NewsAPI采集节点
            $nodeId = $this->findNewsAPINode();
            
            if (!$nodeId) {
                $this->log("错误：未找到夏威夷新闻采集-NewsAPI节点");
                return false;
            }
            
            $this->log("找到采集节点ID: " . $nodeId);
            
            // 执行采集
            $result = $this->spider->executeCollection($nodeId);
            
            if ($result) {
                $this->log("采集任务执行成功");
                return true;
            } else {
                $this->log("采集任务执行失败");
                return false;
            }
            
        } catch (Exception $e) {
            $this->log("采集任务执行异常: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 查找NewsAPI采集节点ID
     */
    private function findNewsAPINode() {
        global $db;
        
        $sql = "SELECT id FROM " . DB_PREFIX . "spider_node WHERE name LIKE '%NewsAPI%' AND status = 1 LIMIT 1";
        $result = $db->query($sql);
        
        if ($row = $db->fetch_array($result)) {
            return $row['id'];
        }
        
        return false;
    }
    
    /**
     * 记录日志
     */
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$message}\n";
        
        // 写入日志文件
        file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
        
        // 同时输出到控制台（用于调试）
        echo $logMessage;
    }
}

// 执行采集任务
if (php_sapi_name() === 'cli' || defined('CRON_RUNNING')) {
    $collector = new NewsAPICollection();
    $collector->execute();
} else {
    // 通过Web访问时的处理
    if (isset($_GET['action']) && $_GET['action'] === 'run') {
        define('CRON_RUNNING', true);
        $collector = new NewsAPICollection();
        $result = $collector->execute();
        
        if ($result) {
            echo json_encode(['status' => 'success', 'message' => '采集任务执行成功']);
        } else {
            echo json_encode(['status' => 'error', 'message' => '采集任务执行失败']);
        }
    } else {
        echo "NewsAPI采集任务执行文件";
    }
}
?>
