{"hawaii_news_now": {"name": "Hawaii News Now", "list_url": "https://www.hawaiinewsnow.com/news/", "list_selector": ".story-item h3 a", "rules": {"title": "h1.headline, h1.entry-title", "content": ".story-body, .entry-content", "date": ".timestamp, .entry-date", "author": ".byline, .author", "source": "Hawaii News Now"}, "encoding": "utf-8"}, "honolulu_star_advertiser": {"name": "Honolulu Star-Advertiser", "list_url": "https://www.staradvertiser.com/category/breaking-news/", "list_selector": ".post-title a", "rules": {"title": "h1.entry-title", "content": ".entry-content", "date": ".entry-date", "author": ".author-name", "source": "Honolulu Star-Advertiser"}, "encoding": "utf-8"}, "hawaii_tribune_herald": {"name": "Hawaii Tribune-Herald", "list_url": "https://www.hawaiitribune-herald.com/news/", "list_selector": ".tnt-headline a", "rules": {"title": "h1.tnt-headline", "content": ".tnt-content", "date": ".tnt-date", "author": ".tnt-byline", "source": "Hawaii Tribune-Herald"}, "encoding": "utf-8"}, "indeed_hawaii_jobs": {"name": "Indeed Hawaii Jobs", "list_url": "https://www.indeed.com/jobs?q=&l=Hawaii", "list_selector": "[data-jk] h2 a", "rules": {"title": "h1.jobsearch-JobInfoHeader-title", "company": ".icl-u-lg-mr--sm", "location": "[data-testid='job-location']", "salary": ".icl-u-xs-mr--xs", "description": "#jobDescriptionText", "source": "Indeed Hawaii"}, "encoding": "utf-8"}, "craigslist_honolulu": {"name": "Craigslist Honolulu", "list_url": "https://honolulu.craigslist.org/search/sss", "list_selector": ".result-row .result-title", "rules": {"title": "#titletextonly", "price": ".price", "location": ".postingtitletext small", "description": "#postingbody", "date": ".postinginfos time", "source": "Craigslist Honolulu"}, "encoding": "utf-8"}, "hawaii_free_press": {"name": "Hawaii Free Press", "list_url": "https://www.hawaiifreepress.com/", "list_selector": ".post-title a", "rules": {"title": ".post-title", "content": ".post-content", "date": ".post-date", "author": ".post-author", "source": "Hawaii Free Press"}, "encoding": "utf-8"}, "big_island_now": {"name": "Big Island Now", "list_url": "https://bigislandnow.com/category/news/", "list_selector": ".entry-title a", "rules": {"title": "h1.entry-title", "content": ".entry-content", "date": ".entry-date", "author": ".author-name", "source": "Big Island Now"}, "encoding": "utf-8"}, "maui_now": {"name": "Maui Now", "list_url": "https://mauinow.com/category/news/", "list_selector": ".entry-title a", "rules": {"title": "h1.entry-title", "content": ".entry-content", "date": ".entry-date", "author": ".author-name", "source": "Maui Now"}, "encoding": "utf-8"}}